package main

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"

	"customer-invoice-api/api"
	"customer-invoice-api/models"
	"customer-invoice-api/pkg/log"
	"customer-invoice-api/pkg/setting"
	"customer-invoice-api/services"
)

func init() {
	// Initialize configuration
	setting.Setup()

	// Initialize logger
	log.Setup(&log.ConfigLogger{
		Mode:     log.DevMode,
		Encoding: log.EncodingConsole,
		ZapType:  log.ZapTypeSugar,
		Level: lo.If(
			setting.ServerSetting.RunMode == setting.RunModeRelease,
			log.InfoLevel,
		).Else(log.DebugLevel),
		SavePath:         setting.AppSetting.LogSavePath,
		SaveName:         setting.AppSetting.LogSaveName,
		SaveTimeFormat:   setting.AppSetting.LogSaveTimeFormat,
		FileExt:          setting.AppSetting.LogFileExt,
		FileMaxSizeInMB:  setting.AppSetting.LogFileMaxSizeInMB,
		FileMaxAgeInDays: setting.AppSetting.LogFileMaxAgeInDays,
		FileMaxBackups:   setting.AppSetting.LogFileMaxBackups,
		Compress:         setting.AppSetting.LogCompressEnabled,
	})

	// Initialize database models
	models.Setup()

	// Initialize services
	services.Setup()
}

// @title						Customer Invoice APIs
// @version						1.0
// @description					The API server for Customer Invoice Management System.
// @host						localhost:8080
// @BasePath					/api/v1
func main() {
	gin.SetMode(setting.ServerSetting.RunMode)

	// Initialize router
	routersInit := api.InitRouter()

	// Server configuration
	readTimeout := setting.ServerSetting.ReadTimeout
	writeTimeout := setting.ServerSetting.WriteTimeout
	port := setting.ServerSetting.HttpPort

	// Override port from environment if provided
	portStr := os.Getenv("PORT")
	if portStr != "" {
		port, _ = strconv.Atoi(portStr)
	}

	endPoint := fmt.Sprintf("%s:%d", setting.ServerSetting.Host, port)
	maxHeaderBytes := 1 << 20

	server := &http.Server{
		Addr:           endPoint,
		Handler:        routersInit,
		ReadTimeout:    readTimeout,
		WriteTimeout:   writeTimeout,
		MaxHeaderBytes: maxHeaderBytes,
	}

	// Run database migrations
	models.MigrateDatabase()

	// Start HTTP server
	go func() {
		log.Infof("Start http server listening %s", endPoint)
		if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Fatalf("listen: %s\n", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt)
	<-quit
	log.Infof("Shutdown Server ...")

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server Shutdown: %v", err)
	}
	log.Infof("Server exiting")
}
