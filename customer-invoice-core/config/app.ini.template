[server]
# debug or release
RunMode = debug
Host = 0.0.0.0
HttpPort = 8080
ReadTimeout = 60
WriteTimeout = 60
BuildName = 1.0.0

[database]
Type = postgres
User = postgres
Password = 123123
Host = 127.0.0.1
Port = 5432
Name = customer_invoice_db
SSLMode = disable

[app]
Name = "Customer Invoice API"

RuntimeRootPath = runtime/

DefaultPerPage = 10

LogMode = debug
LogSavePath = runtime/logs/
LogSaveName = log
LogFileExt = log
LogSaveTimeFormat = 20060102
LogFileMaxSizeInMB = 100
LogFileMaxAgeInDays = 200
LogFileMaxBackups = 30
LogCompressEnabled = true

[jwt]
Secret = "your-super-secret-jwt-key-change-this-in-production"
ExpirationHours = 24
Issuer = "customer-invoice-api"
