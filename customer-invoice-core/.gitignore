# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
bin/
dist/

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out
coverage.html

# Dependency directories
vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Runtime files
runtime/
logs/
*.log

# Environment files
.env
.env.local
.env.*.local

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/

# Build artifacts
main
customer-invoice-api

# Test files
test_results/
coverage/

# Documentation build
docs/_build/

# Backup files
*.bak
*.backup

# Cache directories
.cache/

# Local development
local/
dev/

# Certificates and keys
*.pem
*.key
*.crt
*.p12
*.pfx

# Secrets
secrets/
*.secret
