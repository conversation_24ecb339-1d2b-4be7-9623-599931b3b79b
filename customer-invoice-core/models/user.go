package models

import (
	"time"

	"gorm.io/gorm"
)

// UserRole represents the role of a user
type UserRole string

const (
	UserRoleAdmin UserRole = "admin"
)

// User represents a user in the system
type User struct {
	ID           uint      `gorm:"primaryKey" json:"id"`
	Username     string    `gorm:"unique;not null;size:50" json:"username"`
	PasswordHash string    `gorm:"not null;size:255" json:"-"` // Don't include in JSON
	Role         UserRole  `gorm:"size:20;not null;default:'admin'" json:"role"`
	IsActive     bool      `gorm:"default:true" json:"is_active"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// UserQuery represents query parameters for user operations
type UserQuery struct {
	ID       *uint
	Username *string
	Role     *UserRole
	IsActive *bool
}

// UserRepositoryIface defines the interface for user repository operations
type UserRepositoryIface interface {
	Create(user *User, tx *gorm.DB) error
	Update(user *User, tx *gorm.DB) error
	FindByID(id uint, options *FindOneOptions, tx *gorm.DB) (*User, error)
	FindByUsername(username string, options *FindOneOptions, tx *gorm.DB) (*User, error)
	FindOne(query *UserQuery, options *FindOneOptions, tx *gorm.DB) (*User, error)
	FindMany(query *UserQuery, options *FindManyOptions, tx *gorm.DB) ([]*User, error)
	FindPage(query *UserQuery, options *FindPageOptions, tx *gorm.DB) ([]*User, *Pagination, error)
	Delete(id uint, tx *gorm.DB) error
	Count(query *UserQuery, tx *gorm.DB) (int64, error)
}

// UserRepository implements UserRepositoryIface
type UserRepository struct{}

// Create creates a new user
func (r *UserRepository) Create(user *User, tx *gorm.DB) error {
	db := getDBOrTx(tx)
	return db.Create(user).Error
}

// Update updates a user
func (r *UserRepository) Update(user *User, tx *gorm.DB) error {
	db := getDBOrTx(tx)
	return db.Save(user).Error
}

// FindByID finds a user by ID
func (r *UserRepository) FindByID(id uint, options *FindOneOptions, tx *gorm.DB) (*User, error) {
	db := getDBOrTx(tx)
	var user User

	query := db.Where("id = ?", id)

	if options != nil {
		if len(options.Preload) > 0 {
			for _, preload := range options.Preload {
				query = query.Preload(preload)
			}
		}
		if len(options.Select) > 0 {
			query = query.Select(options.Select)
		}
		if len(options.Omit) > 0 {
			query = query.Omit(options.Omit...)
		}
	}

	err := query.First(&user).Error
	if err != nil {
		return nil, err
	}

	return &user, nil
}

// FindByUsername finds a user by username
func (r *UserRepository) FindByUsername(username string, options *FindOneOptions, tx *gorm.DB) (*User, error) {
	db := getDBOrTx(tx)
	var user User

	query := db.Where("username = ?", username)

	if options != nil {
		if len(options.Preload) > 0 {
			for _, preload := range options.Preload {
				query = query.Preload(preload)
			}
		}
		if len(options.Select) > 0 {
			query = query.Select(options.Select)
		}
		if len(options.Omit) > 0 {
			query = query.Omit(options.Omit...)
		}
	}

	err := query.First(&user).Error
	if err != nil {
		return nil, err
	}

	return &user, nil
}

// FindOne finds a single user based on query
func (r *UserRepository) FindOne(query *UserQuery, options *FindOneOptions, tx *gorm.DB) (*User, error) {
	db := getDBOrTx(tx)
	var user User

	dbQuery := db.Model(&User{})

	// Apply query filters
	if query != nil {
		if query.ID != nil {
			dbQuery = dbQuery.Where("id = ?", *query.ID)
		}
		if query.Username != nil {
			dbQuery = dbQuery.Where("username = ?", *query.Username)
		}
		if query.Role != nil {
			dbQuery = dbQuery.Where("role = ?", *query.Role)
		}
		if query.IsActive != nil {
			dbQuery = dbQuery.Where("is_active = ?", *query.IsActive)
		}
	}

	// Apply options
	if options != nil {
		if len(options.Preload) > 0 {
			for _, preload := range options.Preload {
				dbQuery = dbQuery.Preload(preload)
			}
		}
		if len(options.Select) > 0 {
			dbQuery = dbQuery.Select(options.Select)
		}
		if len(options.Omit) > 0 {
			dbQuery = dbQuery.Omit(options.Omit...)
		}
	}

	err := dbQuery.First(&user).Error
	if err != nil {
		return nil, err
	}

	return &user, nil
}

// FindMany finds multiple users based on query
func (r *UserRepository) FindMany(query *UserQuery, options *FindManyOptions, tx *gorm.DB) ([]*User, error) {
	db := getDBOrTx(tx)
	var users []*User

	dbQuery := db.Model(&User{})

	// Apply query filters
	if query != nil {
		if query.ID != nil {
			dbQuery = dbQuery.Where("id = ?", *query.ID)
		}
		if query.Username != nil {
			dbQuery = dbQuery.Where("username = ?", *query.Username)
		}
		if query.Role != nil {
			dbQuery = dbQuery.Where("role = ?", *query.Role)
		}
		if query.IsActive != nil {
			dbQuery = dbQuery.Where("is_active = ?", *query.IsActive)
		}
	}

	// Apply options
	if options != nil {
		if len(options.Preload) > 0 {
			for _, preload := range options.Preload {
				dbQuery = dbQuery.Preload(preload)
			}
		}
		if len(options.Select) > 0 {
			dbQuery = dbQuery.Select(options.Select)
		}
		if len(options.Omit) > 0 {
			dbQuery = dbQuery.Omit(options.Omit...)
		}
		if len(options.OrderBy) > 0 {
			for _, order := range options.OrderBy {
				dbQuery = dbQuery.Order(order)
			}
		}
		if options.Limit > 0 {
			dbQuery = dbQuery.Limit(options.Limit)
		}
		if options.Offset > 0 {
			dbQuery = dbQuery.Offset(options.Offset)
		}
	}

	err := dbQuery.Find(&users).Error
	if err != nil {
		return nil, err
	}

	return users, nil
}

// FindPage finds users with pagination
func (r *UserRepository) FindPage(query *UserQuery, options *FindPageOptions, tx *gorm.DB) ([]*User, *Pagination, error) {
	db := getDBOrTx(tx)
	var users []*User
	var total int64

	dbQuery := db.Model(&User{})

	// Apply query filters for counting
	countQuery := db.Model(&User{})
	if query != nil {
		if query.ID != nil {
			dbQuery = dbQuery.Where("id = ?", *query.ID)
			countQuery = countQuery.Where("id = ?", *query.ID)
		}
		if query.Username != nil {
			dbQuery = dbQuery.Where("username = ?", *query.Username)
			countQuery = countQuery.Where("username = ?", *query.Username)
		}
		if query.Role != nil {
			dbQuery = dbQuery.Where("role = ?", *query.Role)
			countQuery = countQuery.Where("role = ?", *query.Role)
		}
		if query.IsActive != nil {
			dbQuery = dbQuery.Where("is_active = ?", *query.IsActive)
			countQuery = countQuery.Where("is_active = ?", *query.IsActive)
		}
	}

	// Count total records
	err := countQuery.Count(&total).Error
	if err != nil {
		return nil, nil, err
	}

	// Apply pagination and options
	if options != nil {
		if len(options.Preload) > 0 {
			for _, preload := range options.Preload {
				dbQuery = dbQuery.Preload(preload)
			}
		}
		if len(options.Select) > 0 {
			dbQuery = dbQuery.Select(options.Select)
		}
		if len(options.Omit) > 0 {
			dbQuery = dbQuery.Omit(options.Omit...)
		}
		if len(options.OrderBy) > 0 {
			for _, order := range options.OrderBy {
				dbQuery = dbQuery.Order(order)
			}
		}

		// Apply pagination
		offset := (options.Page - 1) * options.PerPage
		dbQuery = dbQuery.Offset(offset).Limit(options.PerPage)
	}

	err = dbQuery.Find(&users).Error
	if err != nil {
		return nil, nil, err
	}

	// Calculate pagination info
	pagination := &Pagination{
		Page:       options.Page,
		PerPage:    options.PerPage,
		Total:      total,
		TotalPages: int((total + int64(options.PerPage) - 1) / int64(options.PerPage)),
	}

	return users, pagination, nil
}

// Delete deletes a user by ID
func (r *UserRepository) Delete(id uint, tx *gorm.DB) error {
	db := getDBOrTx(tx)
	return db.Delete(&User{}, id).Error
}

// Count counts users based on query
func (r *UserRepository) Count(query *UserQuery, tx *gorm.DB) (int64, error) {
	db := getDBOrTx(tx)
	var count int64

	dbQuery := db.Model(&User{})

	if query != nil {
		if query.ID != nil {
			dbQuery = dbQuery.Where("id = ?", *query.ID)
		}
		if query.Username != nil {
			dbQuery = dbQuery.Where("username = ?", *query.Username)
		}
		if query.Role != nil {
			dbQuery = dbQuery.Where("role = ?", *query.Role)
		}
		if query.IsActive != nil {
			dbQuery = dbQuery.Where("is_active = ?", *query.IsActive)
		}
	}

	err := dbQuery.Count(&count).Error
	return count, err
}
