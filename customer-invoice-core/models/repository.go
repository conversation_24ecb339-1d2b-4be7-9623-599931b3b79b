package models

// AppRepository aggregates all repository interfaces
type AppRepository struct {
	Customer CustomerRepositoryIface
	Invoice  InvoiceRepositoryIface
	User     UserRepositoryIface
}

// Repository is the global repository instance
var Repository *AppRepository

// InitRepository initializes all repositories
func InitRepository() {
	Repository = &AppRepository{
		Customer: &CustomerRepository{},
		Invoice:  &InvoiceRepository{},
		User:     &UserRepository{},
	}
}
