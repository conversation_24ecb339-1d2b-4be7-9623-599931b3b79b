package models

import (
	"fmt"
	"log"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"customer-invoice-api/pkg/setting"
)

var db *gorm.DB

// Pagination represents pagination information
type Pagination struct {
	Page       int   `json:"page"`
	PerPage    int   `json:"per_page"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
}

// FindOneOptions represents options for finding one record
type FindOneOptions struct {
	Preload []string
	Select  []string
	Omit    []string
}

// FindManyOptions represents options for finding many records
type FindManyOptions struct {
	Preload []string
	Select  []string
	Omit    []string
	OrderBy []string
	Limit   int
	Offset  int
}

// FindPageOptions represents options for paginated queries
type FindPageOptions struct {
	Preload []string
	Select  []string
	Omit    []string
	OrderBy []string
	Page    int
	PerPage int
}

// Setup initializes the database connection
func Setup() {
	var err error

	dsn := fmt.Sprintf("host=%s port=%s user=%s dbname=%s password=%s sslmode=%s",
		setting.DatabaseSetting.Host,
		setting.DatabaseSetting.Port,
		setting.DatabaseSetting.User,
		setting.DatabaseSetting.Name,
		setting.DatabaseSetting.Password,
		setting.DatabaseSetting.SSLMode,
	)

	// Configure GORM logger
	var gormLogger logger.Interface
	if setting.ServerSetting.RunMode == setting.RunModeDebug {
		gormLogger = logger.Default.LogMode(logger.Info)
	} else {
		gormLogger = logger.Default.LogMode(logger.Silent)
	}

	db, err = gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
	})

	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("Failed to get database instance: %v", err)
	}

	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	log.Println("Database connection established successfully")
}

// GetDB returns the database instance
func GetDB() *gorm.DB {
	return db
}

// MigrateDatabase runs database migrations
func MigrateDatabase() {
	log.Println("Running database migrations...")

	err := db.AutoMigrate(
		&Customer{},
		&Invoice{},
		&User{},
	)

	if err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// Initialize repositories after migration
	InitRepository()

	log.Println("Database migrations completed successfully")
}

// IsRecordNotFound checks if error is record not found
func IsRecordNotFound(err error) bool {
	return err == gorm.ErrRecordNotFound
}

// Transaction helper
func Transaction(fn func(*gorm.DB) error) error {
	tx := db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	if err := fn(tx); err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// Helper functions for repository operations
func getDBOrTx(tx *gorm.DB) *gorm.DB {
	if tx != nil {
		return tx
	}
	return db
}

func applyFindOneOptions(query *gorm.DB, options *FindOneOptions) *gorm.DB {
	if options.Preload != nil {
		for _, preload := range options.Preload {
			query = query.Preload(preload)
		}
	}
	if options.Select != nil {
		query = query.Select(options.Select)
	}
	if options.Omit != nil {
		query = query.Omit(options.Omit...)
	}
	return query
}

func applyFindManyOptions(query *gorm.DB, options *FindManyOptions) *gorm.DB {
	if options.Preload != nil {
		for _, preload := range options.Preload {
			query = query.Preload(preload)
		}
	}
	if options.Select != nil {
		query = query.Select(options.Select)
	}
	if options.Omit != nil {
		query = query.Omit(options.Omit...)
	}
	if options.OrderBy != nil {
		for _, order := range options.OrderBy {
			query = query.Order(order)
		}
	}
	if options.Limit > 0 {
		query = query.Limit(options.Limit)
	}
	if options.Offset > 0 {
		query = query.Offset(options.Offset)
	}
	return query
}

func applyFindPageOptions(query *gorm.DB, options *FindPageOptions) *gorm.DB {
	if options.Preload != nil {
		for _, preload := range options.Preload {
			query = query.Preload(preload)
		}
	}
	if options.Select != nil {
		query = query.Select(options.Select)
	}
	if options.Omit != nil {
		query = query.Omit(options.Omit...)
	}
	if options.OrderBy != nil {
		for _, order := range options.OrderBy {
			query = query.Order(order)
		}
	}

	// Apply pagination
	if options.PerPage > 0 {
		offset := (options.Page - 1) * options.PerPage
		query = query.Limit(options.PerPage).Offset(offset)
	}

	return query
}

func calculatePagination(total int64, page, perPage int) *Pagination {
	if perPage <= 0 {
		perPage = setting.AppSetting.DefaultPerPage
	}
	if page <= 0 {
		page = 1
	}

	totalPages := int((total + int64(perPage) - 1) / int64(perPage))

	return &Pagination{
		Page:       page,
		PerPage:    perPage,
		Total:      total,
		TotalPages: totalPages,
	}
}
