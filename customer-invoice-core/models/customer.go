package models

import (
	"time"
	"gorm.io/gorm"
)

// Customer represents a customer in the system
type Customer struct {
	ID        uint      `gorm:"primaryKey" json:"id"`
	Name      string    `gorm:"not null;size:255" json:"name"`
	Address   string    `gorm:"type:text" json:"address"`
	Phone     string    `gorm:"size:20" json:"phone"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// Relationships
	Invoices []Invoice `gorm:"foreignKey:CustomerID" json:"invoices,omitempty"`
}

// CustomerQuery represents query parameters for customer operations
type CustomerQuery struct {
	ID     *uint
	Name   *string
	Phone  *string
	Search *string // For searching by name, address, or phone
}

// CustomerRepositoryIface defines customer repository operations
type CustomerRepositoryIface interface {
	Create(customer *Customer, tx *gorm.DB) error
	CreateMany(customers []*Customer, tx *gorm.DB) error
	Update(customer *Customer, tx *gorm.DB) error
	FindByID(id uint, options *FindOneOptions) (*Customer, error)
	FindByName(name string, options *FindOneOptions) (*Customer, error)
	FindOne(query *CustomerQuery, options *FindOneOptions) (*Customer, error)
	FindPage(query *CustomerQuery, options *FindPageOptions) ([]*Customer, *Pagination, error)
	FindMany(query *CustomerQuery, options *FindManyOptions) ([]*Customer, error)
	Count(query *CustomerQuery) (int64, error)
	Delete(id uint, tx *gorm.DB) error
	SoftDelete(id uint, tx *gorm.DB) error
}

// CustomerRepository implements CustomerRepositoryIface
type CustomerRepository struct{}

// Create creates a new customer
func (r *CustomerRepository) Create(customer *Customer, tx *gorm.DB) error {
	db := getDBOrTx(tx)
	return db.Create(customer).Error
}

// CreateMany creates multiple customers
func (r *CustomerRepository) CreateMany(customers []*Customer, tx *gorm.DB) error {
	db := getDBOrTx(tx)
	return db.Create(customers).Error
}

// Update updates a customer
func (r *CustomerRepository) Update(customer *Customer, tx *gorm.DB) error {
	db := getDBOrTx(tx)
	return db.Save(customer).Error
}

// FindByID finds a customer by ID
func (r *CustomerRepository) FindByID(id uint, options *FindOneOptions) (*Customer, error) {
	var customer Customer
	query := db.Where("id = ?", id)
	
	if options != nil {
		query = applyFindOneOptions(query, options)
	}
	
	err := query.First(&customer).Error
	return &customer, err
}

// FindByName finds a customer by name
func (r *CustomerRepository) FindByName(name string, options *FindOneOptions) (*Customer, error) {
	var customer Customer
	query := db.Where("name ILIKE ?", "%"+name+"%")
	
	if options != nil {
		query = applyFindOneOptions(query, options)
	}
	
	err := query.First(&customer).Error
	return &customer, err
}

// FindOne finds a single customer based on query
func (r *CustomerRepository) FindOne(query *CustomerQuery, options *FindOneOptions) (*Customer, error) {
	var customer Customer
	dbQuery := db.Model(&Customer{})
	
	dbQuery = applyCustomerQuery(dbQuery, query)
	
	if options != nil {
		dbQuery = applyFindOneOptions(dbQuery, options)
	}
	
	err := dbQuery.First(&customer).Error
	return &customer, err
}

// FindPage finds customers with pagination
func (r *CustomerRepository) FindPage(query *CustomerQuery, options *FindPageOptions) ([]*Customer, *Pagination, error) {
	var customers []*Customer
	var total int64
	
	dbQuery := db.Model(&Customer{})
	dbQuery = applyCustomerQuery(dbQuery, query)
	
	// Count total records
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, nil, err
	}
	
	// Apply pagination and options
	if options != nil {
		dbQuery = applyFindPageOptions(dbQuery, options)
	}
	
	err := dbQuery.Find(&customers).Error
	if err != nil {
		return nil, nil, err
	}
	
	pagination := calculatePagination(total, options.Page, options.PerPage)
	return customers, pagination, nil
}

// FindMany finds multiple customers
func (r *CustomerRepository) FindMany(query *CustomerQuery, options *FindManyOptions) ([]*Customer, error) {
	var customers []*Customer
	dbQuery := db.Model(&Customer{})
	
	dbQuery = applyCustomerQuery(dbQuery, query)
	
	if options != nil {
		dbQuery = applyFindManyOptions(dbQuery, options)
	}
	
	err := dbQuery.Find(&customers).Error
	return customers, err
}

// Count counts customers based on query
func (r *CustomerRepository) Count(query *CustomerQuery) (int64, error) {
	var count int64
	dbQuery := db.Model(&Customer{})
	
	dbQuery = applyCustomerQuery(dbQuery, query)
	
	err := dbQuery.Count(&count).Error
	return count, err
}

// Delete hard deletes a customer
func (r *CustomerRepository) Delete(id uint, tx *gorm.DB) error {
	db := getDBOrTx(tx)
	return db.Unscoped().Delete(&Customer{}, id).Error
}

// SoftDelete soft deletes a customer
func (r *CustomerRepository) SoftDelete(id uint, tx *gorm.DB) error {
	db := getDBOrTx(tx)
	return db.Delete(&Customer{}, id).Error
}

// Helper function to apply customer query conditions
func applyCustomerQuery(query *gorm.DB, customerQuery *CustomerQuery) *gorm.DB {
	if customerQuery == nil {
		return query
	}
	
	if customerQuery.ID != nil {
		query = query.Where("id = ?", *customerQuery.ID)
	}
	if customerQuery.Name != nil {
		query = query.Where("name ILIKE ?", "%"+*customerQuery.Name+"%")
	}
	if customerQuery.Phone != nil {
		query = query.Where("phone ILIKE ?", "%"+*customerQuery.Phone+"%")
	}
	if customerQuery.Search != nil && *customerQuery.Search != "" {
		searchTerm := "%" + *customerQuery.Search + "%"
		query = query.Where("name ILIKE ? OR address ILIKE ? OR phone ILIKE ?", 
			searchTerm, searchTerm, searchTerm)
	}
	
	return query
}
