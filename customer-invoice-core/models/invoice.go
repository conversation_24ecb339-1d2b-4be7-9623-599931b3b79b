package models

import (
	"time"
	"gorm.io/gorm"
	"github.com/shopspring/decimal"
)

// InvoiceStatus represents the status of an invoice
type InvoiceStatus string

const (
	InvoiceStatusSent     InvoiceStatus = "Sent"
	InvoiceStatusPaid     InvoiceStatus = "Paid"
	InvoiceStatusCanceled InvoiceStatus = "Canceled"
)

// Invoice represents an invoice in the system
type Invoice struct {
	ID         uint            `gorm:"primaryKey" json:"id"`
	Total      decimal.Decimal `gorm:"type:decimal(10,2);not null" json:"total"`
	Payment    string          `gorm:"size:50" json:"payment"`
	Status     InvoiceStatus   `gorm:"size:20;not null;check:status IN ('Sent','Paid','Canceled')" json:"status"`
	Created    time.Time       `gorm:"type:date;not null;default:CURRENT_DATE" json:"created"`
	CustomerID uint            `gorm:"not null;index" json:"customer_id"`
	CreatedAt  time.Time       `json:"created_at"`
	UpdatedAt  time.Time       `json:"updated_at"`
	
	// Relationships
	Customer Customer `gorm:"foreignKey:CustomerID" json:"customer,omitempty"`
}

// InvoiceQuery represents query parameters for invoice operations
type InvoiceQuery struct {
	ID         *uint
	CustomerID *uint
	Status     *InvoiceStatus
	Payment    *string
	MinTotal   *decimal.Decimal
	MaxTotal   *decimal.Decimal
	DateFrom   *time.Time
	DateTo     *time.Time
	Search     *string
}

// InvoiceStats represents invoice statistics
type InvoiceStats struct {
	TotalCount    int64           `json:"total_count"`
	TotalAmount   decimal.Decimal `json:"total_amount"`
	PaidCount     int64           `json:"paid_count"`
	PaidAmount    decimal.Decimal `json:"paid_amount"`
	SentCount     int64           `json:"sent_count"`
	SentAmount    decimal.Decimal `json:"sent_amount"`
	CanceledCount int64           `json:"canceled_count"`
	CanceledAmount decimal.Decimal `json:"canceled_amount"`
}

// InvoiceRepositoryIface defines invoice repository operations
type InvoiceRepositoryIface interface {
	Create(invoice *Invoice, tx *gorm.DB) error
	CreateMany(invoices []*Invoice, tx *gorm.DB) error
	Update(invoice *Invoice, tx *gorm.DB) error
	FindByID(id uint, options *FindOneOptions) (*Invoice, error)
	FindOne(query *InvoiceQuery, options *FindOneOptions) (*Invoice, error)
	FindPage(query *InvoiceQuery, options *FindPageOptions) ([]*Invoice, *Pagination, error)
	FindMany(query *InvoiceQuery, options *FindManyOptions) ([]*Invoice, error)
	Count(query *InvoiceQuery) (int64, error)
	Delete(id uint, tx *gorm.DB) error
	SoftDelete(id uint, tx *gorm.DB) error
	GetStatsByCustomer(customerID uint) (*InvoiceStats, error)
	GetTotalByCustomer(customerID uint) (decimal.Decimal, error)
	CountByCustomer(customerID uint) (int64, error)
}

// InvoiceRepository implements InvoiceRepositoryIface
type InvoiceRepository struct{}

// Create creates a new invoice
func (r *InvoiceRepository) Create(invoice *Invoice, tx *gorm.DB) error {
	db := getDBOrTx(tx)
	return db.Create(invoice).Error
}

// CreateMany creates multiple invoices
func (r *InvoiceRepository) CreateMany(invoices []*Invoice, tx *gorm.DB) error {
	db := getDBOrTx(tx)
	return db.Create(invoices).Error
}

// Update updates an invoice
func (r *InvoiceRepository) Update(invoice *Invoice, tx *gorm.DB) error {
	db := getDBOrTx(tx)
	return db.Save(invoice).Error
}

// FindByID finds an invoice by ID
func (r *InvoiceRepository) FindByID(id uint, options *FindOneOptions) (*Invoice, error) {
	var invoice Invoice
	query := db.Where("id = ?", id)
	
	if options != nil {
		query = applyFindOneOptions(query, options)
	}
	
	err := query.First(&invoice).Error
	return &invoice, err
}

// FindOne finds a single invoice based on query
func (r *InvoiceRepository) FindOne(query *InvoiceQuery, options *FindOneOptions) (*Invoice, error) {
	var invoice Invoice
	dbQuery := db.Model(&Invoice{})
	
	dbQuery = applyInvoiceQuery(dbQuery, query)
	
	if options != nil {
		dbQuery = applyFindOneOptions(dbQuery, options)
	}
	
	err := dbQuery.First(&invoice).Error
	return &invoice, err
}

// FindPage finds invoices with pagination
func (r *InvoiceRepository) FindPage(query *InvoiceQuery, options *FindPageOptions) ([]*Invoice, *Pagination, error) {
	var invoices []*Invoice
	var total int64
	
	dbQuery := db.Model(&Invoice{})
	dbQuery = applyInvoiceQuery(dbQuery, query)
	
	// Count total records
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, nil, err
	}
	
	// Apply pagination and options
	if options != nil {
		dbQuery = applyFindPageOptions(dbQuery, options)
	}
	
	err := dbQuery.Find(&invoices).Error
	if err != nil {
		return nil, nil, err
	}
	
	pagination := calculatePagination(total, options.Page, options.PerPage)
	return invoices, pagination, nil
}

// FindMany finds multiple invoices
func (r *InvoiceRepository) FindMany(query *InvoiceQuery, options *FindManyOptions) ([]*Invoice, error) {
	var invoices []*Invoice
	dbQuery := db.Model(&Invoice{})
	
	dbQuery = applyInvoiceQuery(dbQuery, query)
	
	if options != nil {
		dbQuery = applyFindManyOptions(dbQuery, options)
	}
	
	err := dbQuery.Find(&invoices).Error
	return invoices, err
}

// Count counts invoices based on query
func (r *InvoiceRepository) Count(query *InvoiceQuery) (int64, error) {
	var count int64
	dbQuery := db.Model(&Invoice{})
	
	dbQuery = applyInvoiceQuery(dbQuery, query)
	
	err := dbQuery.Count(&count).Error
	return count, err
}

// Delete hard deletes an invoice
func (r *InvoiceRepository) Delete(id uint, tx *gorm.DB) error {
	db := getDBOrTx(tx)
	return db.Unscoped().Delete(&Invoice{}, id).Error
}

// SoftDelete soft deletes an invoice
func (r *InvoiceRepository) SoftDelete(id uint, tx *gorm.DB) error {
	db := getDBOrTx(tx)
	return db.Delete(&Invoice{}, id).Error
}

// GetStatsByCustomer gets invoice statistics for a customer
func (r *InvoiceRepository) GetStatsByCustomer(customerID uint) (*InvoiceStats, error) {
	var stats InvoiceStats
	
	// Get total count and amount
	err := db.Model(&Invoice{}).
		Where("customer_id = ?", customerID).
		Select("COUNT(*) as total_count, COALESCE(SUM(total), 0) as total_amount").
		Scan(&stats).Error
	if err != nil {
		return nil, err
	}
	
	// Get stats by status
	var statusStats []struct {
		Status InvoiceStatus   `json:"status"`
		Count  int64           `json:"count"`
		Amount decimal.Decimal `json:"amount"`
	}
	
	err = db.Model(&Invoice{}).
		Where("customer_id = ?", customerID).
		Select("status, COUNT(*) as count, COALESCE(SUM(total), 0) as amount").
		Group("status").
		Scan(&statusStats).Error
	if err != nil {
		return nil, err
	}
	
	// Map status stats
	for _, stat := range statusStats {
		switch stat.Status {
		case InvoiceStatusPaid:
			stats.PaidCount = stat.Count
			stats.PaidAmount = stat.Amount
		case InvoiceStatusSent:
			stats.SentCount = stat.Count
			stats.SentAmount = stat.Amount
		case InvoiceStatusCanceled:
			stats.CanceledCount = stat.Count
			stats.CanceledAmount = stat.Amount
		}
	}
	
	return &stats, nil
}

// GetTotalByCustomer gets total amount of invoices for a customer
func (r *InvoiceRepository) GetTotalByCustomer(customerID uint) (decimal.Decimal, error) {
	var total decimal.Decimal
	err := db.Model(&Invoice{}).
		Where("customer_id = ?", customerID).
		Select("COALESCE(SUM(total), 0)").
		Scan(&total).Error
	return total, err
}

// CountByCustomer counts invoices for a customer
func (r *InvoiceRepository) CountByCustomer(customerID uint) (int64, error) {
	var count int64
	err := db.Model(&Invoice{}).
		Where("customer_id = ?", customerID).
		Count(&count).Error
	return count, err
}

// Helper function to apply invoice query conditions
func applyInvoiceQuery(query *gorm.DB, invoiceQuery *InvoiceQuery) *gorm.DB {
	if invoiceQuery == nil {
		return query
	}
	
	if invoiceQuery.ID != nil {
		query = query.Where("id = ?", *invoiceQuery.ID)
	}
	if invoiceQuery.CustomerID != nil {
		query = query.Where("customer_id = ?", *invoiceQuery.CustomerID)
	}
	if invoiceQuery.Status != nil {
		query = query.Where("status = ?", *invoiceQuery.Status)
	}
	if invoiceQuery.Payment != nil {
		query = query.Where("payment ILIKE ?", "%"+*invoiceQuery.Payment+"%")
	}
	if invoiceQuery.MinTotal != nil {
		query = query.Where("total >= ?", *invoiceQuery.MinTotal)
	}
	if invoiceQuery.MaxTotal != nil {
		query = query.Where("total <= ?", *invoiceQuery.MaxTotal)
	}
	if invoiceQuery.DateFrom != nil {
		query = query.Where("created >= ?", *invoiceQuery.DateFrom)
	}
	if invoiceQuery.DateTo != nil {
		query = query.Where("created <= ?", *invoiceQuery.DateTo)
	}
	if invoiceQuery.Search != nil && *invoiceQuery.Search != "" {
		searchTerm := "%" + *invoiceQuery.Search + "%"
		query = query.Where("payment ILIKE ? OR status::text ILIKE ?", searchTerm, searchTerm)
	}
	
	return query
}
