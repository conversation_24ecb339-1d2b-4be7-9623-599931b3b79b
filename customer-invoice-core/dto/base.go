package dto

// BaseResponse represents a standard API response
type BaseResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   *ErrorInfo  `json:"error,omitempty"`
}

// ErrorInfo represents error information in API responses
type ErrorInfo struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// PaginatedResponse represents a paginated API response
type PaginatedResponse struct {
	Success    bool        `json:"success"`
	Message    string      `json:"message,omitempty"`
	Data       interface{} `json:"data"`
	Pagination interface{} `json:"pagination"`
	Error      *ErrorInfo  `json:"error,omitempty"`
}

// ListResponse represents a list API response
type ListResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data"`
	Count   int         `json:"count"`
	Error   *ErrorInfo  `json:"error,omitempty"`
}

// IDRequest represents a request with just an ID
type IDRequest struct {
	ID uint `uri:"id" binding:"required" json:"id"`
}

// PaginationRequest represents pagination parameters
type PaginationRequest struct {
	Page    int `form:"page,default=1" json:"page"`
	PerPage int `form:"per_page,default=10" json:"per_page"`
}

// SearchRequest represents search parameters
type SearchRequest struct {
	Search  string `form:"search" json:"search"`
	OrderBy string `form:"order_by" json:"order_by"`
	Sort    string `form:"sort,default=asc" json:"sort"`
}

// CombinedRequest combines pagination and search
type CombinedRequest struct {
	PaginationRequest
	SearchRequest
}

// Pagination represents pagination information (same as models.Pagination but for DTOs)
type Pagination struct {
	Page       int   `json:"page"`
	PerPage    int   `json:"per_page"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
}
