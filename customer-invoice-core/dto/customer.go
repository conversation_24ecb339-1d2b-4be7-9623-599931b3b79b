package dto

import (
	"time"
	"github.com/shopspring/decimal"
)

// Customer DTOs for API requests and responses

// CreateCustomerRequest represents the request to create a customer
type CreateCustomerRequest struct {
	Name    string `json:"name" binding:"required,min=1,max=255"`
	Address string `json:"address,omitempty"`
	Phone   string `json:"phone,omitempty,max=20"`
}

// UpdateCustomerRequest represents the request to update a customer
type UpdateCustomerRequest struct {
	Name    *string `json:"name,omitempty" binding:"omitempty,min=1,max=255"`
	Address *string `json:"address,omitempty"`
	Phone   *string `json:"phone,omitempty,max=20"`
}

// CustomerResponse represents the customer response
type CustomerResponse struct {
	ID        uint      `json:"id"`
	Name      string    `json:"name"`
	Address   string    `json:"address"`
	Phone     string    `json:"phone"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// CustomerWithStatsResponse represents customer with invoice statistics
type CustomerWithStatsResponse struct {
	CustomerResponse
	InvoiceCount  int64           `json:"invoice_count"`
	TotalAmount   decimal.Decimal `json:"total_amount"`
	PaidAmount    decimal.Decimal `json:"paid_amount"`
	PendingAmount decimal.Decimal `json:"pending_amount"`
}

// CustomerListRequest represents the request to list customers
type CustomerListRequest struct {
	CombinedRequest
	Name  *string `form:"name" json:"name"`
	Phone *string `form:"phone" json:"phone"`
}

// CustomerSearchRequest represents the request to search customers
type CustomerSearchRequest struct {
	Search string `form:"search" json:"search" binding:"required,min=1"`
	Limit  int    `form:"limit,default=10" json:"limit"`
}
