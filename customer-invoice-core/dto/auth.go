package dto

import "customer-invoice-api/models"

// LoginRequest represents the login request payload
type LoginRequest struct {
	Username string `json:"username" binding:"required" example:"admin"`
	Password string `json:"password" binding:"required" example:"admin@123456"`
}

// LoginResponse represents the login response
type LoginResponse struct {
	Token     string      `json:"token"`
	TokenType string      `json:"token_type"`
	ExpiresIn int64       `json:"expires_in"`
	User      UserProfile `json:"user"`
}

// UserProfile represents user profile information
type UserProfile struct {
	ID       uint             `json:"id"`
	Username string           `json:"username"`
	Role     models.UserRole  `json:"role"`
	IsActive bool             `json:"is_active"`
}

// RefreshTokenRequest represents the refresh token request
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// AuthResponse represents a generic auth response
type AuthResponse struct {
	BaseResponse
	Data *LoginResponse `json:"data,omitempty"`
}

// UserProfileResponse represents user profile response
type UserProfileResponse struct {
	BaseResponse
	Data *UserProfile `json:"data,omitempty"`
}
