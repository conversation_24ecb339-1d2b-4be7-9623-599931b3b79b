package dto

import (
	"customer-invoice-api/models"
	"time"

	"github.com/shopspring/decimal"
)

// Invoice DTOs for API requests and responses

// CreateInvoiceRequest represents the request to create an invoice
type CreateInvoiceRequest struct {
	Total      decimal.Decimal      `json:"total" binding:"required"`
	Payment    string               `json:"payment" binding:"required,max=50"`
	Status     models.InvoiceStatus `json:"status" binding:"required,oneof=Sent Paid Canceled"`
	Created    *time.Time           `json:"created,omitempty"`
	CustomerID uint                 `json:"customer_id" binding:"required"`
}

// UpdateInvoiceRequest represents the request to update an invoice
type UpdateInvoiceRequest struct {
	Total   *decimal.Decimal      `json:"total,omitempty"`
	Payment *string               `json:"payment,omitempty" binding:"omitempty,max=50"`
	Status  *models.InvoiceStatus `json:"status,omitempty" binding:"omitempty,oneof=Sent Paid Canceled"`
	Created *time.Time            `json:"created,omitempty"`
}

// InvoiceResponse represents the invoice response
type InvoiceResponse struct {
	ID         uint                 `json:"id"`
	Total      decimal.Decimal      `json:"total"`
	Payment    string               `json:"payment"`
	Status     models.InvoiceStatus `json:"status"`
	Created    time.Time            `json:"created"`
	CustomerID uint                 `json:"customer_id"`
	CreatedAt  time.Time            `json:"created_at"`
	UpdatedAt  time.Time            `json:"updated_at"`

	// Optional customer information
	Customer *CustomerResponse `json:"customer,omitempty"`
}

// InvoiceListRequest represents the request to list invoices
type InvoiceListRequest struct {
	CombinedRequest
	CustomerID *uint                 `form:"customer_id" json:"customer_id"`
	Status     *models.InvoiceStatus `form:"status" json:"status" binding:"omitempty,oneof=Sent Paid Canceled"`
	Payment    *string               `form:"payment" json:"payment"`
	MinTotal   *decimal.Decimal      `form:"min_total" json:"min_total"`
	MaxTotal   *decimal.Decimal      `form:"max_total" json:"max_total"`
	DateFrom   *time.Time            `form:"date_from" json:"date_from" time_format:"2006-01-02"`
	DateTo     *time.Time            `form:"date_to" json:"date_to" time_format:"2006-01-02"`
}

// InvoiceSearchRequest represents the request to search invoices
type InvoiceSearchRequest struct {
	CustomerID uint                  `uri:"customer_id" binding:"required"`
	Status     *models.InvoiceStatus `form:"status" json:"status" binding:"omitempty,oneof=Sent Paid Canceled"`
	Payment    *string               `form:"payment" json:"payment"`
	MinTotal   *decimal.Decimal      `form:"min_total" json:"min_total"`
	MaxTotal   *decimal.Decimal      `form:"max_total" json:"max_total"`
	Search     *string               `form:"search" json:"search"`
	Page       int                   `form:"page,default=1" json:"page"`
	PerPage    int                   `form:"per_page,default=10" json:"per_page"`
}

// InvoiceStatsResponse represents invoice statistics response
type InvoiceStatsResponse struct {
	CustomerID     uint            `json:"customer_id"`
	TotalCount     int64           `json:"total_count"`
	TotalAmount    decimal.Decimal `json:"total_amount"`
	PaidCount      int64           `json:"paid_count"`
	PaidAmount     decimal.Decimal `json:"paid_amount"`
	SentCount      int64           `json:"sent_count"`
	SentAmount     decimal.Decimal `json:"sent_amount"`
	CanceledCount  int64           `json:"canceled_count"`
	CanceledAmount decimal.Decimal `json:"canceled_amount"`
}

// CustomerInvoicesResponse represents customer invoices with stats
type CustomerInvoicesResponse struct {
	Customer   CustomerResponse     `json:"customer"`
	Stats      InvoiceStatsResponse `json:"stats"`
	Invoices   []InvoiceResponse    `json:"invoices"`
	Pagination *Pagination          `json:"pagination,omitempty"`
}
