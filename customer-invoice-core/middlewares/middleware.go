package middlewares

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"customer-invoice-api/dto"
	"customer-invoice-api/pkg/log"
	"customer-invoice-api/pkg/setting"
	"customer-invoice-api/services"
)

// GinLogger returns a gin.HandlerFunc for logging
func GinLogger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		log.Info("HTTP Request",
			zap.String("method", param.Method),
			zap.String("path", param.Path),
			zap.Int("status", param.StatusCode),
			zap.Duration("latency", param.Latency),
			zap.String("client_ip", param.ClientIP),
			zap.String("user_agent", param.Request.UserAgent()),
		)
		return ""
	})
}

// GinRecovery returns a gin.HandlerFunc for recovery
func GinRecovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		log.Error("Panic recovered",
			zap.Any("error", recovered),
			zap.String("path", c.Request.URL.Path),
			zap.String("method", c.Request.Method),
		)
		c.AbortWithStatus(http.StatusInternalServerError)
	})
}

// CORSMiddleware handles CORS
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")

		// Allow all origins in development, specific origins in production
		if setting.ServerSetting.RunMode == setting.RunModeDebug {
			c.Header("Access-Control-Allow-Origin", "*")
		} else {
			// Add your allowed origins here
			allowedOrigins := []string{
				"https://yourdomain.com",
				"https://www.yourdomain.com",
			}

			for _, allowedOrigin := range allowedOrigins {
				if origin == allowedOrigin {
					c.Header("Access-Control-Allow-Origin", origin)
					break
				}
			}
		}

		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// JWTAuthMiddleware validates JWT tokens
func JWTAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, dto.BaseResponse{
				Success: false,
				Error: &dto.ErrorInfo{
					Code:    "MISSING_TOKEN",
					Message: "Authorization header is required",
					Details: "No authorization header provided",
				},
			})
			c.Abort()
			return
		}

		// Check if the header starts with "Bearer "
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.JSON(http.StatusUnauthorized, dto.BaseResponse{
				Success: false,
				Error: &dto.ErrorInfo{
					Code:    "INVALID_TOKEN_FORMAT",
					Message: "Invalid authorization header format",
					Details: "Authorization header must start with 'Bearer '",
				},
			})
			c.Abort()
			return
		}

		// Extract the token
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == "" {
			c.JSON(http.StatusUnauthorized, dto.BaseResponse{
				Success: false,
				Error: &dto.ErrorInfo{
					Code:    "EMPTY_TOKEN",
					Message: "Token is empty",
					Details: "No token provided after 'Bearer '",
				},
			})
			c.Abort()
			return
		}

		// Validate the token
		claims, appErr := services.Auth.ValidateToken(tokenString)
		if appErr != nil {
			c.JSON(appErr.HTTPStatus, dto.BaseResponse{
				Success: false,
				Error: &dto.ErrorInfo{
					Code:    "INVALID_TOKEN",
					Message: appErr.Message,
					Details: appErr.Details,
				},
			})
			c.Abort()
			return
		}

		// Store user information in context
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("user_role", claims.Role)

		c.Next()
	}
}
