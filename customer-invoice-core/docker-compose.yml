version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - STAGE=dev
      - DB_PASSWORD=postgres
    depends_on:
      - postgres
    volumes:
      - ./runtime/logs:/app/runtime/logs
    networks:
      - customer-invoice-network

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: customer_invoice_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./scripts/seed.sql:/docker-entrypoint-initdb.d/02-seed.sql
    networks:
      - customer-invoice-network

  # Optional: pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - customer-invoice-network

volumes:
  postgres_data:

networks:
  customer-invoice-network:
    driver: bridge
