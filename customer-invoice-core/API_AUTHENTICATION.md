# Customer Invoice API - Authentication & Usage Guide

## 🔐 Authentication

This API uses JWT (JSON Web Token) authentication. All customer and invoice endpoints require authentication.

### Admin Credentials
- **Username**: `admin`
- **Password**: `admin@123456`

## 📋 API Endpoints

### Base URL
```
http://localhost:8080
```

---

## 🔑 Authentication Endpoints

### 1. Admin Login
**POST** `/api/v1/auth/login`

Login with admin credentials to get a JWT token.

```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin@123456"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 86400,
    "user": {
      "id": 1,
      "username": "admin",
      "role": "admin",
      "is_active": true
    }
  }
}
```

### 2. Get Current User Profile
**GET** `/api/v1/auth/me` (Protected)

Get the profile of the currently authenticated user.

```bash
curl -X GET http://localhost:8080/api/v1/auth/me \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

**Response:**
```json
{
  "success": true,
  "message": "Profile retrieved successfully",
  "data": {
    "id": 1,
    "username": "admin",
    "role": "admin",
    "is_active": true
  }
}
```

---

## 👥 Customer Endpoints (Protected)

All customer endpoints require authentication. Include the JWT token in the Authorization header.

### 1. Get All Customers
**GET** `/api/v1/customers`

```bash
curl -X GET http://localhost:8080/api/v1/customers \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 2. Get Customer by ID
**GET** `/api/v1/customers/{id}`

```bash
curl -X GET http://localhost:8080/api/v1/customers/1 \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 3. Create New Customer
**POST** `/api/v1/customers`

```bash
curl -X POST http://localhost:8080/api/v1/customers \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Nguyễn Văn Test",
    "address": "123 Test Street, Ho Chi Minh City",
    "phone": "**********"
  }'
```

### 4. Update Customer
**PUT** `/api/v1/customers/{id}`

```bash
curl -X PUT http://localhost:8080/api/v1/customers/1 \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Nguyễn Văn Updated",
    "address": "456 Updated Street",
    "phone": "0987654321"
  }'
```

### 5. Delete Customer
**DELETE** `/api/v1/customers/{id}`

```bash
curl -X DELETE http://localhost:8080/api/v1/customers/1 \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 6. Search Customers
**GET** `/api/v1/customers/search?q={search_term}`

```bash
curl -X GET "http://localhost:8080/api/v1/customers/search?q=Nguyen" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 7. Get Customer Statistics
**GET** `/api/v1/customers/{id}/stats`

```bash
curl -X GET http://localhost:8080/api/v1/customers/1/stats \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 8. Get Customer Invoices
**GET** `/api/v1/customers/{id}/invoices`

```bash
curl -X GET http://localhost:8080/api/v1/customers/1/invoices \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

---

## 📄 Invoice Endpoints (Protected)

### 1. Get All Invoices
**GET** `/api/v1/invoices`

```bash
curl -X GET http://localhost:8080/api/v1/invoices \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 2. Get Invoice by ID
**GET** `/api/v1/invoices/{id}`

```bash
curl -X GET http://localhost:8080/api/v1/invoices/1 \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 3. Create New Invoice
**POST** `/api/v1/invoices`

```bash
curl -X POST http://localhost:8080/api/v1/invoices \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "total": 1500000,
    "payment": "Credit Card",
    "status": "Sent",
    "created": "2024-12-20",
    "customer_id": 1
  }'
```

### 4. Update Invoice
**PUT** `/api/v1/invoices/{id}`

```bash
curl -X PUT http://localhost:8080/api/v1/invoices/1 \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "total": 2000000,
    "payment": "Bank Transfer",
    "status": "Paid",
    "created": "2024-12-20",
    "customer_id": 1
  }'
```

### 5. Delete Invoice
**DELETE** `/api/v1/invoices/{id}`

```bash
curl -X DELETE http://localhost:8080/api/v1/invoices/1 \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

---

## 🔧 System Endpoints (Public)

### Health Check
**GET** `/health` or `/api/v1/health`

```bash
curl -X GET http://localhost:8080/health
```

### API Documentation
**GET** `/swagger/index.html`

Open in browser: http://localhost:8080/swagger/index.html

---

## 📝 Usage Examples

### Complete Workflow Example

1. **Login to get token:**
```bash
TOKEN=$(curl -s -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin@123456"}' | \
  jq -r '.data.token')
```

2. **Create a customer:**
```bash
curl -X POST http://localhost:8080/api/v1/customers \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Customer",
    "address": "123 Test St",
    "phone": "**********"
  }'
```

3. **Create an invoice for the customer:**
```bash
curl -X POST http://localhost:8080/api/v1/invoices \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "total": 1500000,
    "payment": "Credit Card",
    "status": "Sent",
    "created": "2024-12-20",
    "customer_id": 1
  }'
```

4. **Get all customers:**
```bash
curl -X GET http://localhost:8080/api/v1/customers \
  -H "Authorization: Bearer $TOKEN"
```

---

## ⚠️ Error Responses

### Authentication Errors
```json
{
  "success": false,
  "error": {
    "code": "MISSING_TOKEN",
    "message": "Authorization header is required",
    "details": "No authorization header provided"
  }
}
```

### Invalid Credentials
```json
{
  "success": false,
  "error": {
    "code": "LOGIN_FAILED",
    "message": "Invalid username or password",
    "details": "Password verification failed"
  }
}
```

---

## 🔒 Security Notes

- JWT tokens expire after 24 hours
- All customer and invoice endpoints require authentication
- Use HTTPS in production
- Store tokens securely (not in browser localStorage for production)
- The admin password should be changed in production

---

## 🚀 Quick Start

1. Start the server: `go run main.go`
2. Login to get token: Use the login curl command above
3. Copy the token from the response
4. Use the token in subsequent API calls by replacing `YOUR_TOKEN_HERE`

**Happy coding! 🎉**
