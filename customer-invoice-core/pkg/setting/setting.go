package setting

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/go-ini/ini"
)

const (
	RunModeRelease = "release"
	RunModeDebug   = "debug"
)

type App struct {
	Name string

	RuntimeRootPath string

	DefaultPerPage int

	LogSavePath         string
	LogSaveName         string
	LogSaveTimeFormat   string
	LogFileExt          string
	LogFileMaxSizeInMB  int
	LogFileMaxAgeInDays int
	LogFileMaxBackups   int
	LogCompressEnabled  bool
}

type Server struct {
	RunMode      string
	Host         string
	HttpPort     int
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	BuildName    string
}

type Database struct {
	Type     string
	User     string
	Password string
	Host     string
	Name     string
	Port     string
	SSLMode  string
}

type JWT struct {
	Secret          string
	ExpirationHours int
	Issuer          string
}

var AppSetting = &App{}
var ServerSetting = &Server{}
var DatabaseSetting = &Database{}
var JWTSetting = &JWT{}

var cfg *ini.File

func getInitFilename() string {
	stage := os.Getenv("STAGE")
	test := os.Getenv("TEST")
	if test == "true" {
		return "../config/app.ini"
	}
	if stage == "" {
		return "config/app.ini"
	}

	return "config/app-" + stage + ".ini"
}

func IsProduction() bool {
	stage := os.Getenv("STAGE")
	return stage == "prod"
}

// Setup initialize the configuration instance
func Setup() {
	var err error
	fileName := getInitFilename()
	fmt.Println("Loading configuration from:", fileName)
	cfg, err = ini.Load(fileName)
	if err != nil {
		log.Fatalf("setting.Setup, failed to parse '%s': %v", fileName, err)
	}

	mapTo("app", AppSetting)
	mapTo("server", ServerSetting)
	mapTo("database", DatabaseSetting)
	mapTo("jwt", JWTSetting)

	ServerSetting.ReadTimeout = ServerSetting.ReadTimeout * time.Second
	ServerSetting.WriteTimeout = ServerSetting.WriteTimeout * time.Second

	// Process environment variables for sensitive data
	if dbPassword := os.Getenv("DB_PASSWORD"); dbPassword != "" {
		DatabaseSetting.Password = dbPassword
	}
}

// mapTo map section
func mapTo(section string, v interface{}) {
	err := cfg.Section(section).MapTo(v)
	if err != nil {
		log.Fatalf("Cfg.MapTo %s err: %v", section, err)
	}
}
