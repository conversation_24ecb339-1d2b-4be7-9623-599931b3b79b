package util

import (
	"strconv"
	"strings"
)

// StringToUint converts string to uint
func StringToUint(s string) (uint, error) {
	val, err := strconv.ParseUint(s, 10, 32)
	return uint(val), err
}

// StringToInt converts string to int
func StringToInt(s string) (int, error) {
	return strconv.Atoi(s)
}

// TrimSpaces trims spaces from string
func TrimSpaces(s string) string {
	return strings.TrimSpace(s)
}

// IsEmpty checks if string is empty
func IsEmpty(s string) bool {
	return strings.TrimSpace(s) == ""
}

// Contains checks if slice contains item
func Contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
