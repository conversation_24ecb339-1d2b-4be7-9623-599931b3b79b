package log

import (
	"os"
	"path/filepath"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// Log levels
const (
	DebugLevel = "debug"
	InfoLevel  = "info"
	WarnLevel  = "warn"
	ErrorLevel = "error"
	FatalLevel = "fatal"
)

// Log modes
const (
	DevMode  = "dev"
	ProdMode = "prod"
)

// Encoding types
const (
	EncodingJSON    = "json"
	EncodingConsole = "console"
)

// Zap types
const (
	ZapTypeLogger = "logger"
	ZapTypeSugar  = "sugar"
)

// ConfigLogger represents logger configuration
type ConfigLogger struct {
	Mode             string
	Encoding         string
	ZapType          string
	Level            string
	SavePath         string
	SaveName         string
	SaveTimeFormat   string
	FileExt          string
	FileMaxSizeInMB  int
	FileMaxAgeInDays int
	FileMaxBackups   int
	Compress         bool
}

var (
	logger *zap.Logger
	sugar  *zap.SugaredLogger
)

// Setup initializes the logger
func Setup(config *ConfigLogger) {
	// Create log directory if it doesn't exist
	if config.SavePath != "" {
		if err := os.MkdirAll(config.SavePath, 0755); err != nil {
			panic(err)
		}
	}

	// Configure log level
	level := getLogLevel(config.Level)

	// Configure encoder
	var encoder zapcore.Encoder
	if config.Encoding == EncodingJSON {
		encoder = zapcore.NewJSONEncoder(getJSONEncoderConfig())
	} else {
		encoder = zapcore.NewConsoleEncoder(getConsoleEncoderConfig())
	}

	// Configure writers
	var cores []zapcore.Core

	// Console writer
	consoleWriter := zapcore.AddSync(os.Stdout)
	cores = append(cores, zapcore.NewCore(encoder, consoleWriter, level))

	// File writer (if configured)
	if config.SavePath != "" && config.SaveName != "" {
		filename := filepath.Join(config.SavePath, config.SaveName+"."+config.FileExt)

		fileWriter := &lumberjack.Logger{
			Filename:   filename,
			MaxSize:    config.FileMaxSizeInMB,
			MaxAge:     config.FileMaxAgeInDays,
			MaxBackups: config.FileMaxBackups,
			Compress:   config.Compress,
		}

		cores = append(cores, zapcore.NewCore(encoder, zapcore.AddSync(fileWriter), level))
	}

	// Create logger
	core := zapcore.NewTee(cores...)
	logger = zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))
	sugar = logger.Sugar()
}

// getLogLevel converts string level to zapcore.Level
func getLogLevel(level string) zapcore.Level {
	switch level {
	case DebugLevel:
		return zapcore.DebugLevel
	case InfoLevel:
		return zapcore.InfoLevel
	case WarnLevel:
		return zapcore.WarnLevel
	case ErrorLevel:
		return zapcore.ErrorLevel
	case FatalLevel:
		return zapcore.FatalLevel
	default:
		return zapcore.InfoLevel
	}
}

// getJSONEncoderConfig returns JSON encoder configuration
func getJSONEncoderConfig() zapcore.EncoderConfig {
	return zapcore.EncoderConfig{
		TimeKey:        "timestamp",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "message",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}
}

// getConsoleEncoderConfig returns console encoder configuration
func getConsoleEncoderConfig() zapcore.EncoderConfig {
	return zapcore.EncoderConfig{
		TimeKey:        "T",
		LevelKey:       "L",
		NameKey:        "N",
		CallerKey:      "C",
		MessageKey:     "M",
		StacktraceKey:  "S",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalColorLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.StringDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}
}

// Logger functions
func Debug(msg string, fields ...zap.Field) {
	logger.Debug(msg, fields...)
}

func Info(msg string, fields ...zap.Field) {
	logger.Info(msg, fields...)
}

func Warn(msg string, fields ...zap.Field) {
	logger.Warn(msg, fields...)
}

func Error(msg string, fields ...zap.Field) {
	logger.Error(msg, fields...)
}

func Fatal(msg string, fields ...zap.Field) {
	logger.Fatal(msg, fields...)
}

// Sugar logger functions
func Debugf(template string, args ...interface{}) {
	sugar.Debugf(template, args...)
}

func Infof(template string, args ...interface{}) {
	sugar.Infof(template, args...)
}

func Warnf(template string, args ...interface{}) {
	sugar.Warnf(template, args...)
}

func Errorf(template string, args ...interface{}) {
	sugar.Errorf(template, args...)
}

func Fatalf(template string, args ...interface{}) {
	sugar.Fatalf(template, args...)
}

// GetLogger returns the zap logger instance
func GetLogger() *zap.Logger {
	return logger
}

// GetSugar returns the zap sugar logger instance
func GetSugar() *zap.SugaredLogger {
	return sugar
}

// Sync flushes any buffered log entries
func Sync() {
	if logger != nil {
		logger.Sync()
	}
	if sugar != nil {
		sugar.Sync()
	}
}
