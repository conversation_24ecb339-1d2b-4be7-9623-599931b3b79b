package e

import (
	"fmt"
	"net/http"
)

// Error codes
const (
	// Success
	Success = 200

	// Client errors (4xx)
	BadRequest          = 400
	Unauthorized        = 401
	Forbidden           = 403
	NotFound            = 404
	MethodNotAllowed    = 405
	Conflict            = 409
	UnprocessableEntity = 422
	TooManyRequests     = 429

	// Server errors (5xx)
	InternalServerError = 500
	BadGateway          = 502
	ServiceUnavailable  = 503
	GatewayTimeout      = 504

	// Custom application errors
	ValidationError     = 1001
	DatabaseError       = 1002
	CacheError          = 1003
	ExternalServiceError = 1004
	AuthenticationError = 1005
	AuthorizationError  = 1006
	DuplicateError      = 1007
	NotFoundError       = 1008
)

// Error messages
var ErrorMessages = map[int]string{
	Success:             "Success",
	BadRequest:          "Bad Request",
	Unauthorized:        "Unauthorized",
	Forbidden:           "Forbidden",
	NotFound:            "Not Found",
	MethodNotAllowed:    "Method Not Allowed",
	Conflict:            "Conflict",
	UnprocessableEntity: "Unprocessable Entity",
	TooManyRequests:     "Too Many Requests",
	InternalServerError: "Internal Server Error",
	BadGateway:          "Bad Gateway",
	ServiceUnavailable:  "Service Unavailable",
	GatewayTimeout:      "Gateway Timeout",
	ValidationError:     "Validation Error",
	DatabaseError:       "Database Error",
	CacheError:          "Cache Error",
	ExternalServiceError: "External Service Error",
	AuthenticationError: "Authentication Error",
	AuthorizationError:  "Authorization Error",
	DuplicateError:      "Duplicate Entry",
	NotFoundError:       "Resource Not Found",
}

// AppError represents an application error
type AppError struct {
	Code       int    `json:"code"`
	Message    string `json:"message"`
	Details    string `json:"details,omitempty"`
	HTTPStatus int    `json:"-"`
}

// Error implements the error interface
func (e *AppError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("Code: %d, Message: %s, Details: %s", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("Code: %d, Message: %s", e.Code, e.Message)
}

// NewError creates a new AppError
func NewError(code int, message string, details ...string) *AppError {
	httpStatus := getHTTPStatus(code)
	
	if message == "" {
		if msg, exists := ErrorMessages[code]; exists {
			message = msg
		} else {
			message = "Unknown Error"
		}
	}
	
	var detail string
	if len(details) > 0 {
		detail = details[0]
	}
	
	return &AppError{
		Code:       code,
		Message:    message,
		Details:    detail,
		HTTPStatus: httpStatus,
	}
}

// Convenience functions for common errors
func NewError400(code int, message string, details ...string) *AppError {
	err := NewError(code, message, details...)
	err.HTTPStatus = http.StatusBadRequest
	return err
}

func NewError401(code int, message string, details ...string) *AppError {
	err := NewError(code, message, details...)
	err.HTTPStatus = http.StatusUnauthorized
	return err
}

func NewError403(code int, message string, details ...string) *AppError {
	err := NewError(code, message, details...)
	err.HTTPStatus = http.StatusForbidden
	return err
}

func NewError404(code int, message string, details ...string) *AppError {
	err := NewError(code, message, details...)
	err.HTTPStatus = http.StatusNotFound
	return err
}

func NewError409(code int, message string, details ...string) *AppError {
	err := NewError(code, message, details...)
	err.HTTPStatus = http.StatusConflict
	return err
}

func NewError422(code int, message string, details ...string) *AppError {
	err := NewError(code, message, details...)
	err.HTTPStatus = http.StatusUnprocessableEntity
	return err
}

func NewError500(code int, message string, details ...string) *AppError {
	err := NewError(code, message, details...)
	err.HTTPStatus = http.StatusInternalServerError
	return err
}

// getHTTPStatus maps error codes to HTTP status codes
func getHTTPStatus(code int) int {
	switch {
	case code >= 200 && code < 300:
		return http.StatusOK
	case code >= 400 && code < 500:
		return code
	case code >= 500 && code < 600:
		return code
	case code == ValidationError:
		return http.StatusBadRequest
	case code == AuthenticationError:
		return http.StatusUnauthorized
	case code == AuthorizationError:
		return http.StatusForbidden
	case code == NotFoundError:
		return http.StatusNotFound
	case code == DuplicateError:
		return http.StatusConflict
	case code == DatabaseError:
		return http.StatusInternalServerError
	case code == CacheError:
		return http.StatusInternalServerError
	case code == ExternalServiceError:
		return http.StatusBadGateway
	default:
		return http.StatusInternalServerError
	}
}

// GetMessage returns the error message for a given code
func GetMessage(code int) string {
	if msg, exists := ErrorMessages[code]; exists {
		return msg
	}
	return "Unknown Error"
}
