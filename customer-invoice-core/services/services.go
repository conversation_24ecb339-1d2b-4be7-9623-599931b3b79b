package services

import (
	"customer-invoice-api/dto"
	"customer-invoice-api/models"
	"customer-invoice-api/pkg/e"
)

// Service interfaces define the contract for business logic
type AuthServiceIface interface {
	Login(req *dto.LoginRequest) (*dto.LoginResponse, *e.AppError)
	GenerateToken(user *models.User) (string, int64, *e.AppError)
	ValidateToken(tokenString string) (*JWTClaims, *e.AppError)
	GetUserProfile(userID uint) (*dto.UserProfile, *e.AppError)
	HashPassword(password string) (string, error)
	VerifyPassword(hashedPassword, password string) error
}

type CustomerServiceIface interface {
	Create(req *dto.CreateCustomerRequest) (*models.Customer, *e.AppError)
	Update(id uint, req *dto.UpdateCustomerRequest) (*models.Customer, *e.AppError)
	FindByID(id uint) (*models.Customer, *e.AppError)
	FindByName(name string) ([]*models.Customer, *e.AppError)
	Search(searchTerm string, limit int) ([]*models.Customer, *e.AppError)
	FindPage(query *models.CustomerQuery, options *models.FindPageOptions) ([]*models.Customer, *models.Pagination, *e.AppError)
	Delete(id uint) *e.AppError
	GetWithStats(id uint) (*dto.CustomerWithStatsResponse, *e.AppError)
	ToResponse(customer *models.Customer) dto.CustomerResponse
	ToResponseList(customers []*models.Customer) []dto.CustomerResponse
}

type InvoiceServiceIface interface {
	Create(req *dto.CreateInvoiceRequest) (*models.Invoice, *e.AppError)
	Update(id uint, req *dto.UpdateInvoiceRequest) (*models.Invoice, *e.AppError)
	FindByID(id uint, includeCustomer bool) (*models.Invoice, *e.AppError)
	FindByCustomer(customerID uint, page, perPage int) ([]*models.Invoice, *models.Pagination, *e.AppError)
	Search(req *dto.InvoiceSearchRequest) ([]*models.Invoice, *models.Pagination, *e.AppError)
	FindPage(query *models.InvoiceQuery, options *models.FindPageOptions) ([]*models.Invoice, *models.Pagination, *e.AppError)
	Delete(id uint) *e.AppError
	GetStats(customerID uint) (*models.InvoiceStats, *e.AppError)
	GetCustomerInvoices(customerID uint, page, perPage int) (*dto.CustomerInvoicesResponse, *e.AppError)
	ToResponse(invoice *models.Invoice) dto.InvoiceResponse
	ToResponseList(invoices []*models.Invoice) []dto.InvoiceResponse
}

// Global service instances
var Auth AuthServiceIface
var Customer CustomerServiceIface
var Invoice InvoiceServiceIface

// Setup initializes all services
func Setup() {
	Auth = &AuthService{}
	Customer = &CustomerService{}
	Invoice = &InvoiceService{}
}
