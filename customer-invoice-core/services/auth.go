package services

import (
	"time"

	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"

	"customer-invoice-api/dto"
	"customer-invoice-api/models"
	"customer-invoice-api/pkg/e"
	"customer-invoice-api/pkg/setting"
)

// AuthService implements authentication business logic
type AuthService struct{}

// JWTClaims represents the JWT claims
type JWTClaims struct {
	UserID   uint            `json:"user_id"`
	Username string          `json:"username"`
	Role     models.UserRole `json:"role"`
	jwt.RegisteredClaims
}

// Login authenticates a user and returns a JWT token
func (s *AuthService) Login(req *dto.LoginRequest) (*dto.LoginResponse, *e.AppError) {
	// Find user by username
	user, err := models.Repository.User.FindByUsername(req.Username, nil, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError401(e.AuthenticationError, "Invalid username or password", "User not found")
		}
		return nil, e.NewError500(e.DatabaseError, "Failed to find user", err.Error())
	}

	// Check if user is active
	if !user.IsActive {
		return nil, e.NewError401(e.AuthenticationError, "Account is disabled", "User account is not active")
	}

	// Verify password
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password))
	if err != nil {
		return nil, e.NewError401(e.AuthenticationError, "Invalid username or password", "Password verification failed")
	}

	// Generate JWT token
	token, expiresIn, appErr := s.GenerateToken(user)
	if appErr != nil {
		return nil, appErr
	}

	// Create response
	response := &dto.LoginResponse{
		Token:     token,
		TokenType: "Bearer",
		ExpiresIn: expiresIn,
		User: dto.UserProfile{
			ID:       user.ID,
			Username: user.Username,
			Role:     user.Role,
			IsActive: user.IsActive,
		},
	}

	return response, nil
}

// GenerateToken generates a JWT token for the user
func (s *AuthService) GenerateToken(user *models.User) (string, int64, *e.AppError) {
	expirationTime := time.Now().Add(time.Duration(setting.JWTSetting.ExpirationHours) * time.Hour)
	expiresIn := int64(setting.JWTSetting.ExpirationHours * 3600) // Convert to seconds

	claims := &JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     user.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    setting.JWTSetting.Issuer,
			Subject:   user.Username,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(setting.JWTSetting.Secret))
	if err != nil {
		return "", 0, e.NewError500(e.InternalServerError, "Failed to generate token", err.Error())
	}

	return tokenString, expiresIn, nil
}

// ValidateToken validates a JWT token and returns the claims
func (s *AuthService) ValidateToken(tokenString string) (*JWTClaims, *e.AppError) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate the signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, jwt.ErrSignatureInvalid
		}
		return []byte(setting.JWTSetting.Secret), nil
	})

	if err != nil {
		if err == jwt.ErrSignatureInvalid {
			return nil, e.NewError401(e.AuthenticationError, "Invalid token signature", err.Error())
		}
		return nil, e.NewError401(e.AuthenticationError, "Invalid token", err.Error())
	}

	if !token.Valid {
		return nil, e.NewError401(e.AuthenticationError, "Invalid token", "Token is not valid")
	}

	claims, ok := token.Claims.(*JWTClaims)
	if !ok {
		return nil, e.NewError401(e.AuthenticationError, "Invalid token claims", "Could not parse token claims")
	}

	return claims, nil
}

// GetUserProfile returns the user profile for the given user ID
func (s *AuthService) GetUserProfile(userID uint) (*dto.UserProfile, *e.AppError) {
	user, err := models.Repository.User.FindByID(userID, nil, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.NotFoundError, "User not found", "User does not exist")
		}
		return nil, e.NewError500(e.DatabaseError, "Failed to find user", err.Error())
	}

	profile := &dto.UserProfile{
		ID:       user.ID,
		Username: user.Username,
		Role:     user.Role,
		IsActive: user.IsActive,
	}

	return profile, nil
}

// HashPassword hashes a password using bcrypt
func (s *AuthService) HashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedBytes), nil
}

// VerifyPassword verifies a password against its hash
func (s *AuthService) VerifyPassword(hashedPassword, password string) error {
	return bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
}
