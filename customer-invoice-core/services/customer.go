package services

import (
	"customer-invoice-api/dto"
	"customer-invoice-api/models"
	"customer-invoice-api/pkg/e"
)

// CustomerService implements customer business logic
type CustomerService struct{}

// Create creates a new customer
func (s *CustomerService) Create(req *dto.CreateCustomerRequest) (*models.Customer, *e.AppError) {
	customer := &models.Customer{
		Name:    req.Name,
		Address: req.Address,
		Phone:   req.Phone,
	}

	err := models.Repository.Customer.Create(customer, nil)
	if err != nil {
		return nil, e.NewError500(e.DatabaseError, "Failed to create customer", err.Error())
	}

	return customer, nil
}

// Update updates an existing customer
func (s *CustomerService) Update(id uint, req *dto.UpdateCustomerRequest) (*models.Customer, *e.AppError) {
	// Find existing customer
	customer, err := models.Repository.Customer.FindByID(id, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.NotFoundError, "Customer not found")
		}
		return nil, e.NewError500(e.DatabaseError, "Failed to find customer", err.Error())
	}

	// Update fields if provided
	if req.Name != nil {
		customer.Name = *req.Name
	}
	if req.Address != nil {
		customer.Address = *req.Address
	}
	if req.Phone != nil {
		customer.Phone = *req.Phone
	}

	err = models.Repository.Customer.Update(customer, nil)
	if err != nil {
		return nil, e.NewError500(e.DatabaseError, "Failed to update customer", err.Error())
	}

	return customer, nil
}

// FindByID finds a customer by ID
func (s *CustomerService) FindByID(id uint) (*models.Customer, *e.AppError) {
	customer, err := models.Repository.Customer.FindByID(id, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.NotFoundError, "Customer not found")
		}
		return nil, e.NewError500(e.DatabaseError, "Failed to find customer", err.Error())
	}

	return customer, nil
}

// FindByName finds customers by name (partial match)
func (s *CustomerService) FindByName(name string) ([]*models.Customer, *e.AppError) {
	query := &models.CustomerQuery{
		Name: &name,
	}

	options := &models.FindManyOptions{
		OrderBy: []string{"name ASC"},
		Limit:   50, // Limit results for performance
	}

	customers, err := models.Repository.Customer.FindMany(query, options)
	if err != nil {
		return nil, e.NewError500(e.DatabaseError, "Failed to search customers", err.Error())
	}

	return customers, nil
}

// Search searches customers by name, address, or phone
func (s *CustomerService) Search(searchTerm string, limit int) ([]*models.Customer, *e.AppError) {
	if limit <= 0 || limit > 100 {
		limit = 10
	}

	query := &models.CustomerQuery{
		Search: &searchTerm,
	}

	options := &models.FindManyOptions{
		OrderBy: []string{"name ASC"},
		Limit:   limit,
	}

	customers, err := models.Repository.Customer.FindMany(query, options)
	if err != nil {
		return nil, e.NewError500(e.DatabaseError, "Failed to search customers", err.Error())
	}

	return customers, nil
}

// FindPage finds customers with pagination
func (s *CustomerService) FindPage(query *models.CustomerQuery, options *models.FindPageOptions) ([]*models.Customer, *models.Pagination, *e.AppError) {
	// Set default ordering if not provided
	if options.OrderBy == nil {
		options.OrderBy = []string{"created_at DESC"}
	}

	customers, pagination, err := models.Repository.Customer.FindPage(query, options)
	if err != nil {
		return nil, nil, e.NewError500(e.DatabaseError, "Failed to fetch customers", err.Error())
	}

	return customers, pagination, nil
}

// Delete deletes a customer
func (s *CustomerService) Delete(id uint) *e.AppError {
	// Check if customer exists
	_, err := models.Repository.Customer.FindByID(id, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return e.NewError404(e.NotFoundError, "Customer not found")
		}
		return e.NewError500(e.DatabaseError, "Failed to find customer", err.Error())
	}

	// Check if customer has invoices
	invoiceCount, err := models.Repository.Invoice.CountByCustomer(id)
	if err != nil {
		return e.NewError500(e.DatabaseError, "Failed to check customer invoices", err.Error())
	}

	if invoiceCount > 0 {
		return e.NewError409(e.Conflict, "Cannot delete customer with existing invoices")
	}

	// Delete customer
	err = models.Repository.Customer.Delete(id, nil)
	if err != nil {
		return e.NewError500(e.DatabaseError, "Failed to delete customer", err.Error())
	}

	return nil
}

// GetWithStats gets customer with invoice statistics
func (s *CustomerService) GetWithStats(id uint) (*dto.CustomerWithStatsResponse, *e.AppError) {
	// Find customer
	customer, err := models.Repository.Customer.FindByID(id, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.NotFoundError, "Customer not found")
		}
		return nil, e.NewError500(e.DatabaseError, "Failed to find customer", err.Error())
	}

	// Get invoice statistics
	stats, err := models.Repository.Invoice.GetStatsByCustomer(id)
	if err != nil {
		return nil, e.NewError500(e.DatabaseError, "Failed to get invoice statistics", err.Error())
	}

	response := &dto.CustomerWithStatsResponse{
		CustomerResponse: dto.CustomerResponse{
			ID:        customer.ID,
			Name:      customer.Name,
			Address:   customer.Address,
			Phone:     customer.Phone,
			CreatedAt: customer.CreatedAt,
			UpdatedAt: customer.UpdatedAt,
		},
		InvoiceCount:  stats.TotalCount,
		TotalAmount:   stats.TotalAmount,
		PaidAmount:    stats.PaidAmount,
		PendingAmount: stats.SentAmount,
	}

	return response, nil
}

// ToResponse converts customer model to response DTO
func (s *CustomerService) ToResponse(customer *models.Customer) dto.CustomerResponse {
	return dto.CustomerResponse{
		ID:        customer.ID,
		Name:      customer.Name,
		Address:   customer.Address,
		Phone:     customer.Phone,
		CreatedAt: customer.CreatedAt,
		UpdatedAt: customer.UpdatedAt,
	}
}

// ToResponseList converts customer models to response DTOs
func (s *CustomerService) ToResponseList(customers []*models.Customer) []dto.CustomerResponse {
	responses := make([]dto.CustomerResponse, len(customers))
	for i, customer := range customers {
		responses[i] = s.ToResponse(customer)
	}
	return responses
}
