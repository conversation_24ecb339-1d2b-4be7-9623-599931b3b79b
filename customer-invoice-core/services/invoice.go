package services

import (
	"customer-invoice-api/dto"
	"customer-invoice-api/models"
	"customer-invoice-api/pkg/e"
	"time"

	"github.com/shopspring/decimal"
)

// InvoiceService implements invoice business logic
type InvoiceService struct{}

// Create creates a new invoice
func (s *InvoiceService) Create(req *dto.CreateInvoiceRequest) (*models.Invoice, *e.AppError) {
	// Validate total amount
	if req.Total.LessThanOrEqual(decimal.Zero) {
		return nil, e.NewError400(e.ValidationError, "Total amount must be greater than 0")
	}

	// Verify customer exists
	_, err := models.Repository.Customer.FindByID(req.CustomerID, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.NotFoundError, "Customer not found")
		}
		return nil, e.NewError500(e.DatabaseError, "Failed to verify customer", err.Error())
	}

	invoice := &models.Invoice{
		Total:      req.Total,
		Payment:    req.Payment,
		Status:     req.Status,
		CustomerID: req.CustomerID,
	}

	// Set created date
	if req.Created != nil {
		invoice.Created = *req.Created
	} else {
		invoice.Created = time.Now()
	}

	err = models.Repository.Invoice.Create(invoice, nil)
	if err != nil {
		return nil, e.NewError500(e.DatabaseError, "Failed to create invoice", err.Error())
	}

	return invoice, nil
}

// Update updates an existing invoice
func (s *InvoiceService) Update(id uint, req *dto.UpdateInvoiceRequest) (*models.Invoice, *e.AppError) {
	// Find existing invoice
	invoice, err := models.Repository.Invoice.FindByID(id, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.NotFoundError, "Invoice not found")
		}
		return nil, e.NewError500(e.DatabaseError, "Failed to find invoice", err.Error())
	}

	// Update fields if provided
	if req.Total != nil {
		if req.Total.LessThanOrEqual(decimal.Zero) {
			return nil, e.NewError400(e.ValidationError, "Total amount must be greater than 0")
		}
		invoice.Total = *req.Total
	}
	if req.Payment != nil {
		invoice.Payment = *req.Payment
	}
	if req.Status != nil {
		invoice.Status = *req.Status
	}
	if req.Created != nil {
		invoice.Created = *req.Created
	}

	err = models.Repository.Invoice.Update(invoice, nil)
	if err != nil {
		return nil, e.NewError500(e.DatabaseError, "Failed to update invoice", err.Error())
	}

	return invoice, nil
}

// FindByID finds an invoice by ID
func (s *InvoiceService) FindByID(id uint, includeCustomer bool) (*models.Invoice, *e.AppError) {
	var options *models.FindOneOptions
	if includeCustomer {
		options = &models.FindOneOptions{
			Preload: []string{"Customer"},
		}
	}

	invoice, err := models.Repository.Invoice.FindByID(id, options)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.NotFoundError, "Invoice not found")
		}
		return nil, e.NewError500(e.DatabaseError, "Failed to find invoice", err.Error())
	}

	return invoice, nil
}

// FindByCustomer finds invoices for a specific customer
func (s *InvoiceService) FindByCustomer(customerID uint, page, perPage int) ([]*models.Invoice, *models.Pagination, *e.AppError) {
	// Verify customer exists
	_, err := models.Repository.Customer.FindByID(customerID, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, nil, e.NewError404(e.NotFoundError, "Customer not found")
		}
		return nil, nil, e.NewError500(e.DatabaseError, "Failed to verify customer", err.Error())
	}

	query := &models.InvoiceQuery{
		CustomerID: &customerID,
	}

	options := &models.FindPageOptions{
		Page:    page,
		PerPage: perPage,
		OrderBy: []string{"created DESC", "created_at DESC"},
		Preload: []string{"Customer"},
	}

	invoices, pagination, err := models.Repository.Invoice.FindPage(query, options)
	if err != nil {
		return nil, nil, e.NewError500(e.DatabaseError, "Failed to fetch invoices", err.Error())
	}

	return invoices, pagination, nil
}

// Search searches invoices for a customer with filters
func (s *InvoiceService) Search(req *dto.InvoiceSearchRequest) ([]*models.Invoice, *models.Pagination, *e.AppError) {
	// Verify customer exists
	_, err := models.Repository.Customer.FindByID(req.CustomerID, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, nil, e.NewError404(e.NotFoundError, "Customer not found")
		}
		return nil, nil, e.NewError500(e.DatabaseError, "Failed to verify customer", err.Error())
	}

	query := &models.InvoiceQuery{
		CustomerID: &req.CustomerID,
		Status:     req.Status,
		Payment:    req.Payment,
		MinTotal:   req.MinTotal,
		MaxTotal:   req.MaxTotal,
		Search:     req.Search,
	}

	options := &models.FindPageOptions{
		Page:    req.Page,
		PerPage: req.PerPage,
		OrderBy: []string{"created DESC", "created_at DESC"},
		Preload: []string{"Customer"},
	}

	invoices, pagination, err := models.Repository.Invoice.FindPage(query, options)
	if err != nil {
		return nil, nil, e.NewError500(e.DatabaseError, "Failed to search invoices", err.Error())
	}

	return invoices, pagination, nil
}

// FindPage finds invoices with pagination
func (s *InvoiceService) FindPage(query *models.InvoiceQuery, options *models.FindPageOptions) ([]*models.Invoice, *models.Pagination, *e.AppError) {
	// Set default ordering if not provided
	if options.OrderBy == nil {
		options.OrderBy = []string{"created DESC", "created_at DESC"}
	}

	// Include customer by default
	if options.Preload == nil {
		options.Preload = []string{"Customer"}
	}

	invoices, pagination, err := models.Repository.Invoice.FindPage(query, options)
	if err != nil {
		return nil, nil, e.NewError500(e.DatabaseError, "Failed to fetch invoices", err.Error())
	}

	return invoices, pagination, nil
}

// Delete deletes an invoice
func (s *InvoiceService) Delete(id uint) *e.AppError {
	// Check if invoice exists
	_, err := models.Repository.Invoice.FindByID(id, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return e.NewError404(e.NotFoundError, "Invoice not found")
		}
		return e.NewError500(e.DatabaseError, "Failed to find invoice", err.Error())
	}

	// Delete invoice
	err = models.Repository.Invoice.Delete(id, nil)
	if err != nil {
		return e.NewError500(e.DatabaseError, "Failed to delete invoice", err.Error())
	}

	return nil
}

// GetStats gets invoice statistics for a customer
func (s *InvoiceService) GetStats(customerID uint) (*models.InvoiceStats, *e.AppError) {
	// Verify customer exists
	_, err := models.Repository.Customer.FindByID(customerID, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.NotFoundError, "Customer not found")
		}
		return nil, e.NewError500(e.DatabaseError, "Failed to verify customer", err.Error())
	}

	stats, err := models.Repository.Invoice.GetStatsByCustomer(customerID)
	if err != nil {
		return nil, e.NewError500(e.DatabaseError, "Failed to get invoice statistics", err.Error())
	}

	return stats, nil
}

// GetCustomerInvoices gets customer with invoices and statistics
func (s *InvoiceService) GetCustomerInvoices(customerID uint, page, perPage int) (*dto.CustomerInvoicesResponse, *e.AppError) {
	// Get customer
	customer, err := models.Repository.Customer.FindByID(customerID, nil)
	if err != nil {
		if models.IsRecordNotFound(err) {
			return nil, e.NewError404(e.NotFoundError, "Customer not found")
		}
		return nil, e.NewError500(e.DatabaseError, "Failed to find customer", err.Error())
	}

	// Get invoices
	invoices, pagination, appErr := s.FindByCustomer(customerID, page, perPage)
	if appErr != nil {
		return nil, appErr
	}

	// Get statistics
	stats, appErr := s.GetStats(customerID)
	if appErr != nil {
		return nil, appErr
	}

	response := &dto.CustomerInvoicesResponse{
		Customer: dto.CustomerResponse{
			ID:        customer.ID,
			Name:      customer.Name,
			Address:   customer.Address,
			Phone:     customer.Phone,
			CreatedAt: customer.CreatedAt,
			UpdatedAt: customer.UpdatedAt,
		},
		Stats: dto.InvoiceStatsResponse{
			CustomerID:     customerID,
			TotalCount:     stats.TotalCount,
			TotalAmount:    stats.TotalAmount,
			PaidCount:      stats.PaidCount,
			PaidAmount:     stats.PaidAmount,
			SentCount:      stats.SentCount,
			SentAmount:     stats.SentAmount,
			CanceledCount:  stats.CanceledCount,
			CanceledAmount: stats.CanceledAmount,
		},
		Invoices: s.ToResponseList(invoices),
		Pagination: &dto.Pagination{
			Page:       pagination.Page,
			PerPage:    pagination.PerPage,
			Total:      pagination.Total,
			TotalPages: pagination.TotalPages,
		},
	}

	return response, nil
}

// ToResponse converts invoice model to response DTO
func (s *InvoiceService) ToResponse(invoice *models.Invoice) dto.InvoiceResponse {
	response := dto.InvoiceResponse{
		ID:         invoice.ID,
		Total:      invoice.Total,
		Payment:    invoice.Payment,
		Status:     invoice.Status,
		Created:    invoice.Created,
		CustomerID: invoice.CustomerID,
		CreatedAt:  invoice.CreatedAt,
		UpdatedAt:  invoice.UpdatedAt,
	}

	// Include customer if loaded
	if invoice.Customer.ID != 0 {
		response.Customer = &dto.CustomerResponse{
			ID:        invoice.Customer.ID,
			Name:      invoice.Customer.Name,
			Address:   invoice.Customer.Address,
			Phone:     invoice.Customer.Phone,
			CreatedAt: invoice.Customer.CreatedAt,
			UpdatedAt: invoice.Customer.UpdatedAt,
		}
	}

	return response
}

// ToResponseList converts invoice models to response DTOs
func (s *InvoiceService) ToResponseList(invoices []*models.Invoice) []dto.InvoiceResponse {
	responses := make([]dto.InvoiceResponse, len(invoices))
	for i, invoice := range invoices {
		responses[i] = s.ToResponse(invoice)
	}
	return responses
}
