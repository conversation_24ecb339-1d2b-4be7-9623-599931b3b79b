-- Sample data for Customer Invoice Database

-- Insert admin user (password: admin@123456)
-- Password hash generated using bcrypt with cost 10
INSERT INTO users (username, password_hash, role, is_active) VALUES
('admin', '$2a$10$XysfEPkhaTZTu9Bt7MFrE.gVo62IZWWLcZOGR0bwWdPKhY3rCQNFS', 'admin', true)
ON CONFLICT (username) DO NOTHING;

-- Insert sample customers
INSERT INTO customers (name, address, phone) VALUES
('<PERSON><PERSON><PERSON><PERSON>', '123 Đường ABC, Quận 1, TP.HCM', '0901234567'),
('Tr<PERSON><PERSON>', '456 Phố XYZ, Hà Nội', '**********'),
('<PERSON><PERSON>', '789 Đường DEF, Đà Nẵng', '**********'),
('<PERSON><PERSON><PERSON>', '321 Đường GHI, <PERSON><PERSON><PERSON>', '**********'),
('<PERSON>àng <PERSON>ăn <PERSON>', '654 <PERSON>ố JKL, <PERSON><PERSON><PERSON>', '**********')
ON CONFLICT DO NOTHING;

-- Insert sample invoices
INSERT INTO invoices (total, payment, status, created, customer_id) VALUES
(1500000, 'Credit Card', 'Paid', '2024-12-01', 1),
(2300000, 'Bank Transfer', 'Sent', '2024-12-05', 2),
(890000, 'Cash', 'Canceled', '2024-12-03', 1),
(3200000, 'Credit Card', 'Paid', '2024-12-07', 3),
(1750000, 'Bank Transfer', 'Paid', '2024-12-08', 2),
(950000, 'Cash', 'Sent', '2024-12-09', 4),
(2100000, 'Credit Card', 'Sent', '2024-12-10', 5),
(1200000, 'Bank Transfer', 'Paid', '2024-12-11', 1),
(800000, 'Cash', 'Canceled', '2024-12-12', 3),
(2800000, 'Credit Card', 'Paid', '2024-12-13', 4),
(1600000, 'Bank Transfer', 'Sent', '2024-12-14', 5),
(1100000, 'Cash', 'Paid', '2024-12-15', 2)
ON CONFLICT DO NOTHING;
