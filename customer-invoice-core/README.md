# Customer Invoice API

Hệ thống quản lý khách hàng và hóa đơn được xây dựng bằng Golang với Gin framework.

## 🚀 Tính năng

### Quản lý khách hàng (Customer Management)
- ✅ **Thêm khách hàng mới** - Tạo thông tin khách hàng với tên, địa chỉ, số điện thoại
- ✅ **Sửa thông tin khách hàng** - Cập nhật thông tin khách hàng hiện có
- ✅ **Xóa khách hàng** - <PERSON><PERSON><PERSON> kh<PERSON>ch hàng (chỉ khi không có hóa đơn)
- ✅ **Tìm khách hàng theo tên** - Tìm kiếm khách hàng bằng tên (hỗ trợ tìm kiếm mờ)
- ✅ **Tìm kiếm khách hàng** - Tìm kiếm theo tên, địa chỉ hoặc số điện thoại

### Quản lý hóa đơ<PERSON> (Invoice Management)
- ✅ **Liệt kê hóa đơn theo khách hàng** - Hi<PERSON>n thị danh sách hóa đơn theo thứ tự mới nhất
- ✅ **Đếm số hóa đơn đã mua** - Thống kê số lượng hóa đơn của khách hàng
- ✅ **Tính tổng tiền các hóa đơn** - Tính tổng giá trị hóa đơn theo trạng thái
- ✅ **Thêm hóa đơn mới** - Tạo hóa đơn cho khách hàng
- ✅ **Sửa hóa đơn** - Cập nhật thông tin hóa đơn
- ✅ **Xóa hóa đơn** - Xóa hóa đơn khỏi hệ thống
- ✅ **Tìm kiếm hóa đơn** - Tìm kiếm theo status, payment, hoặc total

## 🏗️ Kiến trúc

```
customer-invoice-api/
├── api/                    # HTTP API layer
│   ├── v1/                # API version 1
│   └── router.go          # Route definitions
├── dto/                   # Data Transfer Objects
├── models/                # Domain models & Database entities
├── services/              # Business logic layer
├── middlewares/           # HTTP middlewares
├── pkg/                   # Shared packages
├── config/                # Configuration files
├── scripts/               # Database scripts
├── main.go                # Application entry point
└── docker-compose.yml     # Docker setup
```

## 📋 API Endpoints

### Khách hàng (Customers)
```
GET    /api/v1/customers              # Danh sách khách hàng (có phân trang)
GET    /api/v1/customers/search       # Tìm kiếm khách hàng
GET    /api/v1/customers/{id}         # Chi tiết khách hàng
POST   /api/v1/customers              # Tạo khách hàng mới
PUT    /api/v1/customers/{id}         # Cập nhật khách hàng
DELETE /api/v1/customers/{id}         # Xóa khách hàng
GET    /api/v1/customers/{id}/stats   # Thống kê khách hàng
```

### Hóa đơn (Invoices)
```
GET    /api/v1/invoices                           # Danh sách tất cả hóa đơn
GET    /api/v1/invoices/{id}                      # Chi tiết hóa đơn
POST   /api/v1/invoices                           # Tạo hóa đơn mới
PUT    /api/v1/invoices/{id}                      # Cập nhật hóa đơn
DELETE /api/v1/invoices/{id}                      # Xóa hóa đơn

GET    /api/v1/customers/{id}/invoices             # Hóa đơn của khách hàng
GET    /api/v1/customers/{id}/invoices/search      # Tìm kiếm hóa đơn của khách hàng
GET    /api/v1/customers/{id}/invoices/stats       # Thống kê hóa đơn của khách hàng
```

### Hệ thống
```
GET    /health                        # Health check
GET    /swagger/*                     # API Documentation
```

## 🛠️ Cài đặt và chạy

### Yêu cầu
- Go 1.21+
- PostgreSQL 15+
- Docker & Docker Compose (tùy chọn)

### 1. Clone repository
```bash
git clone <repository-url>
cd customer-invoice-api
```

### 2. Cài đặt dependencies
```bash
make deps
```

### 3. Cấu hình database
```bash
# Tạo database
createdb customer_invoice_db

# Chạy schema
psql -d customer_invoice_db -f scripts/schema.sql

# Thêm dữ liệu mẫu
psql -d customer_invoice_db -f scripts/seed.sql
```

### 4. Chạy ứng dụng

#### Development mode
```bash
make run-dev
# hoặc
make start
```

#### Sử dụng Docker
```bash
make docker-up
```

### 5. Truy cập ứng dụng
- API: http://localhost:8080
- Health check: http://localhost:8080/health
- Swagger UI: http://localhost:8080/swagger/index.html
- pgAdmin (nếu dùng Docker): http://localhost:5050

## 📊 Cơ sở dữ liệu

### Bảng customers
```sql
id          SERIAL PRIMARY KEY
name        VARCHAR(255) NOT NULL
address     TEXT
phone       VARCHAR(20)
created_at  TIMESTAMP
updated_at  TIMESTAMP
```

### Bảng invoices
```sql
id          SERIAL PRIMARY KEY
total       DECIMAL(10,2) NOT NULL
payment     VARCHAR(50)
status      VARCHAR(20) CHECK (status IN ('Sent', 'Paid', 'Canceled'))
created     DATE NOT NULL
customer_id INTEGER REFERENCES customers(id)
created_at  TIMESTAMP
updated_at  TIMESTAMP
```

## 🔧 Các lệnh hữu ích

```bash
# Build ứng dụng
make build

# Chạy tests
make test

# Chạy tests với coverage
make test-coverage

# Generate Swagger docs
make swagger

# Format code
make fmt

# Lint code
make lint

# Clean build artifacts
make clean

# Docker commands
make docker-build
make docker-run
make docker-up
make docker-down

# Database commands
make db-setup
make db-seed
```

## 📝 Ví dụ sử dụng API

### 1. Tạo khách hàng mới
```bash
curl -X POST http://localhost:8080/api/v1/customers \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Nguyễn Văn Test",
    "address": "123 Test Street",
    "phone": "0901234567"
  }'
```

### 2. Tìm kiếm khách hàng
```bash
curl "http://localhost:8080/api/v1/customers/search?search=Nguyễn&limit=10"
```

### 3. Tạo hóa đơn mới
```bash
curl -X POST http://localhost:8080/api/v1/invoices \
  -H "Content-Type: application/json" \
  -d '{
    "total": 1500000,
    "payment": "Credit Card",
    "status": "Sent",
    "customer_id": 1
  }'
```

### 4. Lấy hóa đơn của khách hàng
```bash
curl "http://localhost:8080/api/v1/customers/1/invoices?page=1&per_page=10"
```

### 5. Tìm kiếm hóa đơn theo status
```bash
curl "http://localhost:8080/api/v1/customers/1/invoices/search?status=Paid&page=1&per_page=10"
```

### 6. Thống kê hóa đơn của khách hàng
```bash
curl "http://localhost:8080/api/v1/customers/1/invoices/stats"
```

## 🧪 Testing

Chạy tests:
```bash
make test
```

Chạy tests với coverage:
```bash
make test-coverage
```

## 📚 Documentation

- API Documentation: http://localhost:8080/swagger/index.html
- Health Check: http://localhost:8080/health

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Tạo Pull Request

## 📄 License

This project is licensed under the MIT License.
