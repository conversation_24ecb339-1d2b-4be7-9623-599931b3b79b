.PHONY: test build clean run-dev run-prod docker-build docker-run

# Application name
APP_NAME := customer-invoice-api

# Build variables
BUILD_DIR := bin
BINARY_NAME := $(APP_NAME)
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "v1.0.0")
BUILD_TIME := $(shell date +%Y-%m-%dT%H:%M:%S)
LDFLAGS := -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME)"

# Default target
all: build

# Build the application
build:
	@echo "Building $(APP_NAME)..."
	@mkdir -p $(BUILD_DIR)
	go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) main.go

# Clean build artifacts
clean:
	@echo "Cleaning..."
	@rm -rf $(BUILD_DIR)
	@rm -rf runtime/logs/*

# Run tests
test:
	@echo "Running tests..."
	go test -v ./...

# Run tests with coverage
test-coverage:
	@echo "Running tests with coverage..."
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

# Run development server
run-dev:
	@echo "Starting development server..."
	STAGE=dev go run main.go

# Run production server
run-prod:
	@echo "Starting production server..."
	STAGE=prod go run main.go

# Start development server (alias)
start: run-dev

# Install dependencies
deps:
	@echo "Installing dependencies..."
	go mod download
	go mod tidy

# Update dependencies
deps-update:
	@echo "Updating dependencies..."
	go get -u ./...
	go mod tidy

# Generate swagger documentation
swagger:
	@echo "Generating swagger documentation..."
	swag init

# Format code
fmt:
	@echo "Formatting code..."
	go fmt ./...

# Lint code
lint:
	@echo "Linting code..."
	golangci-lint run

# Vet code
vet:
	@echo "Vetting code..."
	go vet ./...

# Docker build
docker-build:
	@echo "Building Docker image..."
	docker build -t $(APP_NAME):$(VERSION) .
	docker tag $(APP_NAME):$(VERSION) $(APP_NAME):latest

# Docker run
docker-run:
	@echo "Running Docker container..."
	docker run -p 8080:8080 --env-file .env $(APP_NAME):latest

# Docker compose up
docker-up:
	@echo "Starting services with docker-compose..."
	docker-compose up -d

# Docker compose down
docker-down:
	@echo "Stopping services with docker-compose..."
	docker-compose down

# Database setup
db-setup:
	@echo "Setting up database..."
	createdb customer_invoice_db || true
	psql -d customer_invoice_db -f scripts/schema.sql

# Database seed
db-seed:
	@echo "Seeding database..."
	psql -d customer_invoice_db -f scripts/seed.sql

# Help
help:
	@echo "Available commands:"
	@echo "  build         - Build the application"
	@echo "  clean         - Clean build artifacts"
	@echo "  test          - Run tests"
	@echo "  test-coverage - Run tests with coverage"
	@echo "  run-dev       - Run development server"
	@echo "  run-prod      - Run production server"
	@echo "  deps          - Install dependencies"
	@echo "  deps-update   - Update dependencies"
	@echo "  swagger       - Generate swagger documentation"
	@echo "  fmt           - Format code"
	@echo "  lint          - Lint code"
	@echo "  vet           - Vet code"
	@echo "  docker-build  - Build Docker image"
	@echo "  docker-run    - Run Docker container"
	@echo "  docker-up     - Start services with docker-compose"
	@echo "  docker-down   - Stop services with docker-compose"
	@echo "  db-setup      - Setup database"
	@echo "  db-seed       - Seed database"
	@echo "  help          - Show this help message"
