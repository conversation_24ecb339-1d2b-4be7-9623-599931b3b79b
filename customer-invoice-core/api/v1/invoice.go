package v1

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"customer-invoice-api/dto"
	"customer-invoice-api/models"
	"customer-invoice-api/services"
)

// GetInvoices godoc
// @Summary Get invoices list
// @Description Get paginated list of invoices
// @Tags invoices
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param per_page query int false "Items per page" default(10)
// @Param customer_id query int false "Filter by customer ID"
// @Param status query string false "Filter by status" Enums(Sent, Paid, Canceled)
// @Param payment query string false "Filter by payment method"
// @Param min_total query number false "Minimum total amount"
// @Param max_total query number false "Maximum total amount"
// @Success 200 {object} dto.PaginatedResponse{data=[]dto.InvoiceResponse}
// @Failure 400 {object} dto.BaseResponse
// @Router /api/v1/invoices [get]
func GetInvoices(c *gin.Context) {
	var req dto.InvoiceListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid query parameters",
				Details: err.Error(),
			},
		})
		return
	}

	// Build query
	query := &models.InvoiceQuery{
		CustomerID: req.CustomerID,
		Status:     req.Status,
		Payment:    req.Payment,
		MinTotal:   req.MinTotal,
		MaxTotal:   req.MaxTotal,
		DateFrom:   req.DateFrom,
		DateTo:     req.DateTo,
	}

	// Build options
	options := &models.FindPageOptions{
		Page:    req.Page,
		PerPage: req.PerPage,
		OrderBy: []string{"created DESC", "created_at DESC"},
		Preload: []string{"Customer"},
	}

	invoices, pagination, appErr := services.Invoice.FindPage(query, options)
	if appErr != nil {
		c.JSON(appErr.HTTPStatus, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "FETCH_FAILED",
				Message: appErr.Message,
				Details: appErr.Details,
			},
		})
		return
	}

	// Convert to response DTOs
	invoiceResponses := services.Invoice.ToResponseList(invoices)

	c.JSON(http.StatusOK, dto.PaginatedResponse{
		Success:    true,
		Message:    "Invoices retrieved successfully",
		Data:       invoiceResponses,
		Pagination: pagination,
	})
}

// GetInvoiceByID godoc
// @Summary Get invoice by ID
// @Description Get a specific invoice by ID
// @Tags invoices
// @Accept json
// @Produce json
// @Param id path int true "Invoice ID"
// @Success 200 {object} dto.BaseResponse{data=dto.InvoiceResponse}
// @Failure 400 {object} dto.BaseResponse
// @Failure 404 {object} dto.BaseResponse
// @Router /api/v1/invoices/{id} [get]
func GetInvoiceByID(c *gin.Context) {
	var req dto.IDRequest
	if err := c.ShouldBindUri(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid invoice ID",
				Details: err.Error(),
			},
		})
		return
	}

	invoice, appErr := services.Invoice.FindByID(req.ID, true)
	if appErr != nil {
		c.JSON(appErr.HTTPStatus, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "INVOICE_NOT_FOUND",
				Message: appErr.Message,
				Details: appErr.Details,
			},
		})
		return
	}

	response := services.Invoice.ToResponse(invoice)

	c.JSON(http.StatusOK, dto.BaseResponse{
		Success: true,
		Message: "Invoice retrieved successfully",
		Data:    response,
	})
}

// CreateInvoice godoc
// @Summary Create a new invoice
// @Description Create a new invoice
// @Tags invoices
// @Accept json
// @Produce json
// @Param request body dto.CreateInvoiceRequest true "Invoice data"
// @Success 201 {object} dto.BaseResponse{data=dto.InvoiceResponse}
// @Failure 400 {object} dto.BaseResponse
// @Failure 404 {object} dto.BaseResponse
// @Router /api/v1/invoices [post]
func CreateInvoice(c *gin.Context) {
	var req dto.CreateInvoiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid request data",
				Details: err.Error(),
			},
		})
		return
	}

	invoice, appErr := services.Invoice.Create(&req)
	if appErr != nil {
		c.JSON(appErr.HTTPStatus, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "CREATE_FAILED",
				Message: appErr.Message,
				Details: appErr.Details,
			},
		})
		return
	}

	// Get invoice with customer info
	invoiceWithCustomer, _ := services.Invoice.FindByID(invoice.ID, true)
	response := services.Invoice.ToResponse(invoiceWithCustomer)

	c.JSON(http.StatusCreated, dto.BaseResponse{
		Success: true,
		Message: "Invoice created successfully",
		Data:    response,
	})
}

// UpdateInvoice godoc
// @Summary Update invoice
// @Description Update an existing invoice
// @Tags invoices
// @Accept json
// @Produce json
// @Param id path int true "Invoice ID"
// @Param request body dto.UpdateInvoiceRequest true "Invoice data"
// @Success 200 {object} dto.BaseResponse{data=dto.InvoiceResponse}
// @Failure 400 {object} dto.BaseResponse
// @Failure 404 {object} dto.BaseResponse
// @Router /api/v1/invoices/{id} [put]
func UpdateInvoice(c *gin.Context) {
	var uriReq dto.IDRequest
	if err := c.ShouldBindUri(&uriReq); err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid invoice ID",
				Details: err.Error(),
			},
		})
		return
	}

	var req dto.UpdateInvoiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid request data",
				Details: err.Error(),
			},
		})
		return
	}

	invoice, appErr := services.Invoice.Update(uriReq.ID, &req)
	if appErr != nil {
		c.JSON(appErr.HTTPStatus, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "UPDATE_FAILED",
				Message: appErr.Message,
				Details: appErr.Details,
			},
		})
		return
	}

	// Get invoice with customer info
	invoiceWithCustomer, _ := services.Invoice.FindByID(invoice.ID, true)
	response := services.Invoice.ToResponse(invoiceWithCustomer)

	c.JSON(http.StatusOK, dto.BaseResponse{
		Success: true,
		Message: "Invoice updated successfully",
		Data:    response,
	})
}

// DeleteInvoice godoc
// @Summary Delete invoice
// @Description Delete an invoice
// @Tags invoices
// @Accept json
// @Produce json
// @Param id path int true "Invoice ID"
// @Success 200 {object} dto.BaseResponse
// @Failure 400 {object} dto.BaseResponse
// @Failure 404 {object} dto.BaseResponse
// @Router /api/v1/invoices/{id} [delete]
func DeleteInvoice(c *gin.Context) {
	var req dto.IDRequest
	if err := c.ShouldBindUri(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid invoice ID",
				Details: err.Error(),
			},
		})
		return
	}

	appErr := services.Invoice.Delete(req.ID)
	if appErr != nil {
		c.JSON(appErr.HTTPStatus, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "DELETE_FAILED",
				Message: appErr.Message,
				Details: appErr.Details,
			},
		})
		return
	}

	c.JSON(http.StatusOK, dto.BaseResponse{
		Success: true,
		Message: "Invoice deleted successfully",
	})
}

// GetCustomerInvoices godoc
// @Summary Get customer invoices
// @Description Get invoices for a specific customer with statistics
// @Tags customers
// @Accept json
// @Produce json
// @Param id path int true "Customer ID"
// @Param page query int false "Page number" default(1)
// @Param per_page query int false "Items per page" default(10)
// @Success 200 {object} dto.BaseResponse{data=dto.CustomerInvoicesResponse}
// @Failure 400 {object} dto.BaseResponse
// @Failure 404 {object} dto.BaseResponse
// @Router /api/v1/customers/{id}/invoices [get]
func GetCustomerInvoices(c *gin.Context) {
	customerIDStr := c.Param("id")
	customerID, err := strconv.ParseUint(customerIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid customer ID",
				Details: err.Error(),
			},
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "10"))

	response, appErr := services.Invoice.GetCustomerInvoices(uint(customerID), page, perPage)
	if appErr != nil {
		c.JSON(appErr.HTTPStatus, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "FETCH_FAILED",
				Message: appErr.Message,
				Details: appErr.Details,
			},
		})
		return
	}

	c.JSON(http.StatusOK, dto.BaseResponse{
		Success: true,
		Message: "Customer invoices retrieved successfully",
		Data:    response,
	})
}

// SearchCustomerInvoices godoc
// @Summary Search customer invoices
// @Description Search invoices for a specific customer with filters
// @Tags customers
// @Accept json
// @Produce json
// @Param id path int true "Customer ID"
// @Param status query string false "Filter by status" Enums(Sent, Paid, Canceled)
// @Param payment query string false "Filter by payment method"
// @Param min_total query number false "Minimum total amount"
// @Param max_total query number false "Maximum total amount"
// @Param search query string false "Search term"
// @Param page query int false "Page number" default(1)
// @Param per_page query int false "Items per page" default(10)
// @Success 200 {object} dto.PaginatedResponse{data=[]dto.InvoiceResponse}
// @Failure 400 {object} dto.BaseResponse
// @Failure 404 {object} dto.BaseResponse
// @Router /api/v1/customers/{id}/invoices/search [get]
func SearchCustomerInvoices(c *gin.Context) {
	customerIDStr := c.Param("id")
	customerID, err := strconv.ParseUint(customerIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid customer ID",
				Details: err.Error(),
			},
		})
		return
	}

	var req dto.InvoiceSearchRequest
	req.CustomerID = uint(customerID)

	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid search parameters",
				Details: err.Error(),
			},
		})
		return
	}

	invoices, pagination, appErr := services.Invoice.Search(&req)
	if appErr != nil {
		c.JSON(appErr.HTTPStatus, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "SEARCH_FAILED",
				Message: appErr.Message,
				Details: appErr.Details,
			},
		})
		return
	}

	invoiceResponses := services.Invoice.ToResponseList(invoices)

	c.JSON(http.StatusOK, dto.PaginatedResponse{
		Success:    true,
		Message:    "Invoices found successfully",
		Data:       invoiceResponses,
		Pagination: pagination,
	})
}

// GetInvoiceStats godoc
// @Summary Get invoice statistics
// @Description Get invoice statistics for a customer
// @Tags customers
// @Accept json
// @Produce json
// @Param id path int true "Customer ID"
// @Success 200 {object} dto.BaseResponse{data=dto.InvoiceStatsResponse}
// @Failure 400 {object} dto.BaseResponse
// @Failure 404 {object} dto.BaseResponse
// @Router /api/v1/customers/{id}/invoices/stats [get]
func GetInvoiceStats(c *gin.Context) {
	customerIDStr := c.Param("id")
	customerID, err := strconv.ParseUint(customerIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid customer ID",
				Details: err.Error(),
			},
		})
		return
	}

	stats, appErr := services.Invoice.GetStats(uint(customerID))
	if appErr != nil {
		c.JSON(appErr.HTTPStatus, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "FETCH_FAILED",
				Message: appErr.Message,
				Details: appErr.Details,
			},
		})
		return
	}

	response := dto.InvoiceStatsResponse{
		CustomerID:     uint(customerID),
		TotalCount:     stats.TotalCount,
		TotalAmount:    stats.TotalAmount,
		PaidCount:      stats.PaidCount,
		PaidAmount:     stats.PaidAmount,
		SentCount:      stats.SentCount,
		SentAmount:     stats.SentAmount,
		CanceledCount:  stats.CanceledCount,
		CanceledAmount: stats.CanceledAmount,
	}

	c.JSON(http.StatusOK, dto.BaseResponse{
		Success: true,
		Message: "Invoice statistics retrieved successfully",
		Data:    response,
	})
}
