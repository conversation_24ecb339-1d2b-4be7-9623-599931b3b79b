package v1

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"customer-invoice-api/dto"
	"customer-invoice-api/models"
	"customer-invoice-api/services"
)

// GetCustomers godoc
// @Summary Get customers list
// @Description Get paginated list of customers
// @Tags customers
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param per_page query int false "Items per page" default(10)
// @Param search query string false "Search term"
// @Param name query string false "Filter by name"
// @Param phone query string false "Filter by phone"
// @Success 200 {object} dto.PaginatedResponse{data=[]dto.CustomerResponse}
// @Failure 400 {object} dto.BaseResponse
// @Router /api/v1/customers [get]
func GetCustomers(c *gin.Context) {
	var req dto.CustomerListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid query parameters",
				Details: err.Error(),
			},
		})
		return
	}

	// Build query
	query := &models.CustomerQuery{
		Name:   req.Name,
		Search: &req.Search,
	}

	// Build options
	options := &models.FindPageOptions{
		Page:    req.Page,
		PerPage: req.PerPage,
		OrderBy: []string{"created_at DESC"},
	}

	customers, pagination, appErr := services.Customer.FindPage(query, options)
	if appErr != nil {
		c.JSON(appErr.HTTPStatus, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "FETCH_FAILED",
				Message: appErr.Message,
				Details: appErr.Details,
			},
		})
		return
	}

	// Convert to response DTOs
	customerResponses := services.Customer.ToResponseList(customers)

	c.JSON(http.StatusOK, dto.PaginatedResponse{
		Success:    true,
		Message:    "Customers retrieved successfully",
		Data:       customerResponses,
		Pagination: pagination,
	})
}

// GetCustomerByID godoc
// @Summary Get customer by ID
// @Description Get a specific customer by ID
// @Tags customers
// @Accept json
// @Produce json
// @Param id path int true "Customer ID"
// @Success 200 {object} dto.BaseResponse{data=dto.CustomerResponse}
// @Failure 400 {object} dto.BaseResponse
// @Failure 404 {object} dto.BaseResponse
// @Router /api/v1/customers/{id} [get]
func GetCustomerByID(c *gin.Context) {
	var req dto.IDRequest
	if err := c.ShouldBindUri(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid customer ID",
				Details: err.Error(),
			},
		})
		return
	}

	customer, appErr := services.Customer.FindByID(req.ID)
	if appErr != nil {
		c.JSON(appErr.HTTPStatus, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "CUSTOMER_NOT_FOUND",
				Message: appErr.Message,
				Details: appErr.Details,
			},
		})
		return
	}

	response := services.Customer.ToResponse(customer)

	c.JSON(http.StatusOK, dto.BaseResponse{
		Success: true,
		Message: "Customer retrieved successfully",
		Data:    response,
	})
}

// CreateCustomer godoc
// @Summary Create a new customer
// @Description Create a new customer
// @Tags customers
// @Accept json
// @Produce json
// @Param request body dto.CreateCustomerRequest true "Customer data"
// @Success 201 {object} dto.BaseResponse{data=dto.CustomerResponse}
// @Failure 400 {object} dto.BaseResponse
// @Router /api/v1/customers [post]
func CreateCustomer(c *gin.Context) {
	var req dto.CreateCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid request data",
				Details: err.Error(),
			},
		})
		return
	}

	customer, appErr := services.Customer.Create(&req)
	if appErr != nil {
		c.JSON(appErr.HTTPStatus, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "CREATE_FAILED",
				Message: appErr.Message,
				Details: appErr.Details,
			},
		})
		return
	}

	response := services.Customer.ToResponse(customer)

	c.JSON(http.StatusCreated, dto.BaseResponse{
		Success: true,
		Message: "Customer created successfully",
		Data:    response,
	})
}

// UpdateCustomer godoc
// @Summary Update customer
// @Description Update an existing customer
// @Tags customers
// @Accept json
// @Produce json
// @Param id path int true "Customer ID"
// @Param request body dto.UpdateCustomerRequest true "Customer data"
// @Success 200 {object} dto.BaseResponse{data=dto.CustomerResponse}
// @Failure 400 {object} dto.BaseResponse
// @Failure 404 {object} dto.BaseResponse
// @Router /api/v1/customers/{id} [put]
func UpdateCustomer(c *gin.Context) {
	var uriReq dto.IDRequest
	if err := c.ShouldBindUri(&uriReq); err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid customer ID",
				Details: err.Error(),
			},
		})
		return
	}

	var req dto.UpdateCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid request data",
				Details: err.Error(),
			},
		})
		return
	}

	customer, appErr := services.Customer.Update(uriReq.ID, &req)
	if appErr != nil {
		c.JSON(appErr.HTTPStatus, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "UPDATE_FAILED",
				Message: appErr.Message,
				Details: appErr.Details,
			},
		})
		return
	}

	response := services.Customer.ToResponse(customer)

	c.JSON(http.StatusOK, dto.BaseResponse{
		Success: true,
		Message: "Customer updated successfully",
		Data:    response,
	})
}

// DeleteCustomer godoc
// @Summary Delete customer
// @Description Delete a customer
// @Tags customers
// @Accept json
// @Produce json
// @Param id path int true "Customer ID"
// @Success 200 {object} dto.BaseResponse
// @Failure 400 {object} dto.BaseResponse
// @Failure 404 {object} dto.BaseResponse
// @Failure 409 {object} dto.BaseResponse
// @Router /api/v1/customers/{id} [delete]
func DeleteCustomer(c *gin.Context) {
	var req dto.IDRequest
	if err := c.ShouldBindUri(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid customer ID",
				Details: err.Error(),
			},
		})
		return
	}

	appErr := services.Customer.Delete(req.ID)
	if appErr != nil {
		c.JSON(appErr.HTTPStatus, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "DELETE_FAILED",
				Message: appErr.Message,
				Details: appErr.Details,
			},
		})
		return
	}

	c.JSON(http.StatusOK, dto.BaseResponse{
		Success: true,
		Message: "Customer deleted successfully",
	})
}

// SearchCustomers godoc
// @Summary Search customers
// @Description Search customers by name, address, or phone
// @Tags customers
// @Accept json
// @Produce json
// @Param search query string true "Search term"
// @Param limit query int false "Limit results" default(10)
// @Success 200 {object} dto.ListResponse{data=[]dto.CustomerResponse}
// @Failure 400 {object} dto.BaseResponse
// @Router /api/v1/customers/search [get]
func SearchCustomers(c *gin.Context) {
	var req dto.CustomerSearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid search parameters",
				Details: err.Error(),
			},
		})
		return
	}

	customers, appErr := services.Customer.Search(req.Search, req.Limit)
	if appErr != nil {
		c.JSON(appErr.HTTPStatus, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "SEARCH_FAILED",
				Message: appErr.Message,
				Details: appErr.Details,
			},
		})
		return
	}

	customerResponses := services.Customer.ToResponseList(customers)

	c.JSON(http.StatusOK, dto.ListResponse{
		Success: true,
		Message: "Customers found successfully",
		Data:    customerResponses,
		Count:   len(customerResponses),
	})
}

// GetCustomerWithStats godoc
// @Summary Get customer with statistics
// @Description Get customer information with invoice statistics
// @Tags customers
// @Accept json
// @Produce json
// @Param id path int true "Customer ID"
// @Success 200 {object} dto.BaseResponse{data=dto.CustomerWithStatsResponse}
// @Failure 400 {object} dto.BaseResponse
// @Failure 404 {object} dto.BaseResponse
// @Router /api/v1/customers/{id}/stats [get]
func GetCustomerWithStats(c *gin.Context) {
	var req dto.IDRequest
	if err := c.ShouldBindUri(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid customer ID",
				Details: err.Error(),
			},
		})
		return
	}

	customerWithStats, appErr := services.Customer.GetWithStats(req.ID)
	if appErr != nil {
		c.JSON(appErr.HTTPStatus, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "FETCH_FAILED",
				Message: appErr.Message,
				Details: appErr.Details,
			},
		})
		return
	}

	c.JSON(http.StatusOK, dto.BaseResponse{
		Success: true,
		Message: "Customer statistics retrieved successfully",
		Data:    customerWithStats,
	})
}
