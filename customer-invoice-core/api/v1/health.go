package v1

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	
	"customer-invoice-api/pkg/setting"
)

type HealthResponse struct {
	Status    string    `json:"status"`
	Timestamp time.Time `json:"timestamp"`
	Version   string    `json:"version"`
	Service   string    `json:"service"`
	Uptime    string    `json:"uptime"`
}

var startTime = time.Now()

// CheckHealth godoc
// @Summary Check service health
// @Description Get the health status of the service
// @Tags health
// @Accept json
// @Produce json
// @Success 200 {object} HealthResponse
// @Router /health [get]
// @Router /api/v1/health [get]
func CheckHealth(c *gin.Context) {
	uptime := time.Since(startTime)
	
	response := HealthResponse{
		Status:    "healthy",
		Timestamp: time.Now(),
		Version:   setting.ServerSetting.BuildName,
		Service:   setting.AppSetting.Name,
		Uptime:    uptime.String(),
	}

	c.J<PERSON>(http.StatusOK, response)
}
