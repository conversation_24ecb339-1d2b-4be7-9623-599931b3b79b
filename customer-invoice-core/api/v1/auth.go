package v1

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"customer-invoice-api/dto"
	"customer-invoice-api/services"
)

// Login godoc
// @Summary Admin login
// @Description Authenticate admin user and return JWT token
// @Tags auth
// @Accept json
// @Produce json
// @Param request body dto.LoginRequest true "Login credentials"
// @Success 200 {object} dto.AuthResponse
// @Failure 400 {object} dto.BaseResponse
// @Failure 401 {object} dto.BaseResponse
// @Router /api/v1/auth/login [post]
func Login(c *gin.Context) {
	var req dto.LoginRequest
	if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "VALIDATION_ERROR",
				Message: "Invalid request data",
				Details: err.Error(),
			},
		})
		return
	}

	// Authenticate user
	loginResponse, appErr := services.Auth.Login(&req)
	if appErr != nil {
		c.JSON(appErr.HTTPStatus, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "LOGIN_FAILED",
				Message: appErr.Message,
				Details: appErr.Details,
			},
		})
		return
	}

	c.JSON(http.StatusOK, dto.BaseResponse{
		Success: true,
		Message: "Login successful",
		Data:    loginResponse,
	})
}

// GetProfile godoc
// @Summary Get current user profile
// @Description Get the profile of the currently authenticated user
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} dto.UserProfileResponse
// @Failure 401 {object} dto.BaseResponse
// @Failure 404 {object} dto.BaseResponse
// @Router /api/v1/auth/me [get]
func GetProfile(c *gin.Context) {
	// Get user ID from context (set by JWT middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "UNAUTHORIZED",
				Message: "User not authenticated",
				Details: "User ID not found in context",
			},
		})
		return
	}

	// Get user profile
	profile, appErr := services.Auth.GetUserProfile(userID.(uint))
	if appErr != nil {
		c.JSON(appErr.HTTPStatus, dto.BaseResponse{
			Success: false,
			Error: &dto.ErrorInfo{
				Code:    "PROFILE_FETCH_FAILED",
				Message: appErr.Message,
				Details: appErr.Details,
			},
		})
		return
	}

	c.JSON(http.StatusOK, dto.BaseResponse{
		Success: true,
		Message: "Profile retrieved successfully",
		Data:    profile,
	})
}
