package api

import (
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	v1 "customer-invoice-api/api/v1"
	"customer-invoice-api/middlewares"
)

// InitRouter initialize routing information
func InitRouter() *gin.Engine {
	r := gin.New()
	r.Use(middlewares.GinLogger())
	r.Use(middlewares.GinRecovery())

	// Health check endpoint
	r.GET("/health", v1.CheckHealth)
	r.GET("/api/v1/health", v1.CheckHealth)

	// CORS middleware
	r.Use(middlewares.CORSMiddleware())

	// Swagger documentation
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// API v1 routes
	apiV1 := r.Group("/api/v1")
	{
		// Auth routes (public)
		auth := apiV1.Group("/auth")
		{
			auth.POST("/login", v1.Login)
		}

		// Protected auth routes
		authProtected := apiV1.Group("/auth")
		authProtected.Use(middlewares.JWTAuthMiddleware())
		{
			authProtected.GET("/me", v1.GetProfile)
		}

		// Customer routes (protected)
		customers := apiV1.Group("/customers")
		customers.Use(middlewares.JWTAuthMiddleware())
		{
			customers.GET("", v1.GetCustomers)
			customers.GET("/search", v1.SearchCustomers)
			customers.POST("", v1.CreateCustomer)
			customers.GET("/:id", v1.GetCustomerByID)
			customers.PUT("/:id", v1.UpdateCustomer)
			customers.DELETE("/:id", v1.DeleteCustomer)
			customers.GET("/:id/stats", v1.GetCustomerWithStats)
			customers.GET("/:id/invoices", v1.GetCustomerInvoices)
			customers.GET("/:id/invoices/search", v1.SearchCustomerInvoices)
			customers.GET("/:id/invoices/stats", v1.GetInvoiceStats)
		}

		// Invoice routes (protected)
		invoices := apiV1.Group("/invoices")
		invoices.Use(middlewares.JWTAuthMiddleware())
		{
			invoices.GET("", v1.GetInvoices)
			invoices.GET("/:id", v1.GetInvoiceByID)
			invoices.POST("", v1.CreateInvoice)
			invoices.PUT("/:id", v1.UpdateInvoice)
			invoices.DELETE("/:id", v1.DeleteInvoice)
		}
	}

	return r
}
