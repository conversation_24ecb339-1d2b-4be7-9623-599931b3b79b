import 'package:json_annotation/json_annotation.dart';
import 'customer.dart';

part 'invoice.g.dart';

// Custom converter for DateTime to RFC3339 format
class DateTimeToRFC3339Converter implements JsonConverter<DateTime?, String?> {
  const DateTimeToRFC3339Converter();

  @override
  DateTime? fromJson(String? json) {
    if (json == null) return null;
    return DateTime.parse(json);
  }

  @override
  String? toJson(DateTime? object) {
    if (object == null) return null;
    // Convert to UTC and format as RFC3339 with Z suffix
    final utc = object.toUtc();
    String isoString = utc.toIso8601String();
    // Ensure it ends with Z
    if (isoString.endsWith('.000Z')) {
      return isoString.replaceAll('.000Z', 'Z');
    } else if (!isoString.endsWith('Z')) {
      return '${isoString}Z';
    }
    return isoString;
  }
}

enum InvoiceStatus {
  @JsonValue('Sent')
  sent,
  @JsonValue('Paid')
  paid,
  @JsonValue('Canceled')
  canceled,
}

extension InvoiceStatusExtension on InvoiceStatus {
  String get displayName {
    switch (this) {
      case InvoiceStatus.sent:
        return 'Sent';
      case InvoiceStatus.paid:
        return 'Paid';
      case InvoiceStatus.canceled:
        return 'Canceled';
    }
  }

  String get value {
    switch (this) {
      case InvoiceStatus.sent:
        return 'Sent';
      case InvoiceStatus.paid:
        return 'Paid';
      case InvoiceStatus.canceled:
        return 'Canceled';
    }
  }
}

@JsonSerializable()
class Invoice {
  final int id;
  @StringToDoubleConverter()
  final double total;
  final String payment;
  final InvoiceStatus status;
  final DateTime created;
  @JsonKey(name: 'customer_id')
  final int customerId;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
  final Customer? customer;

  Invoice({
    required this.id,
    required this.total,
    required this.payment,
    required this.status,
    required this.created,
    required this.customerId,
    required this.createdAt,
    required this.updatedAt,
    this.customer,
  });

  factory Invoice.fromJson(Map<String, dynamic> json) => _$InvoiceFromJson(json);
  Map<String, dynamic> toJson() => _$InvoiceToJson(this);
}

@JsonSerializable()
class CreateInvoiceRequest {
  final double total;
  final String payment;
  final InvoiceStatus status;
  @DateTimeToRFC3339Converter()
  final DateTime? created;
  @JsonKey(name: 'customer_id')
  final int customerId;

  CreateInvoiceRequest({
    required this.total,
    required this.payment,
    required this.status,
    this.created,
    required this.customerId,
  });

  factory CreateInvoiceRequest.fromJson(Map<String, dynamic> json) => 
      _$CreateInvoiceRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CreateInvoiceRequestToJson(this);
}

@JsonSerializable()
class UpdateInvoiceRequest {
  final double? total;
  final String? payment;
  final InvoiceStatus? status;
  @DateTimeToRFC3339Converter()
  final DateTime? created;

  UpdateInvoiceRequest({
    this.total,
    this.payment,
    this.status,
    this.created,
  });

  factory UpdateInvoiceRequest.fromJson(Map<String, dynamic> json) => 
      _$UpdateInvoiceRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateInvoiceRequestToJson(this);
}

@JsonSerializable()
class InvoiceStats {
  @JsonKey(name: 'total_count')
  final int totalCount;
  @JsonKey(name: 'total_amount')
  @StringToDoubleConverter()
  final double totalAmount;
  @JsonKey(name: 'paid_count')
  final int paidCount;
  @JsonKey(name: 'paid_amount')
  @StringToDoubleConverter()
  final double paidAmount;
  @JsonKey(name: 'sent_count')
  final int sentCount;
  @JsonKey(name: 'sent_amount')
  @StringToDoubleConverter()
  final double sentAmount;
  @JsonKey(name: 'canceled_count')
  final int canceledCount;
  @JsonKey(name: 'canceled_amount')
  @StringToDoubleConverter()
  final double canceledAmount;

  InvoiceStats({
    required this.totalCount,
    required this.totalAmount,
    required this.paidCount,
    required this.paidAmount,
    required this.sentCount,
    required this.sentAmount,
    required this.canceledCount,
    required this.canceledAmount,
  });

  factory InvoiceStats.fromJson(Map<String, dynamic> json) => 
      _$InvoiceStatsFromJson(json);
  Map<String, dynamic> toJson() => _$InvoiceStatsToJson(this);
}
