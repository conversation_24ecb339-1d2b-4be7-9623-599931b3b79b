import '../models/customer.dart';
import '../models/api_response.dart';
import 'api_service.dart';

class CustomerService {
  final ApiService _apiService = ApiService.instance;
  
  static CustomerService? _instance;
  static CustomerService get instance => _instance ??= CustomerService._();
  
  CustomerService._();
  
  // Get customers with pagination
  Future<PaginatedCustomersResult> getCustomers({
    int page = 1,
    int perPage = 10,
    String? search,
    String? name,
    String? phone,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'per_page': perPage.toString(),
    };
    
    if (search != null && search.isNotEmpty) {
      queryParams['search'] = search;
    }
    if (name != null && name.isNotEmpty) {
      queryParams['name'] = name;
    }
    if (phone != null && phone.isNotEmpty) {
      queryParams['phone'] = phone;
    }
    
    final response = await _apiService.get('/customers', queryParams: queryParams);
    
    final paginatedResponse = _apiService.handleResponse(
      response,
      (data) => PaginatedResponse<List<Customer>>.fromJson(
        data,
        (json) => (json as List)
            .map((item) => Customer.fromJson(item as Map<String, dynamic>))
            .toList(),
      ),
    );
    
    if (paginatedResponse.success && paginatedResponse.data != null) {
      return PaginatedCustomersResult(
        customers: paginatedResponse.data!,
        pagination: paginatedResponse.pagination!,
      );
    } else {
      throw Exception(paginatedResponse.error?.message ?? 'Failed to get customers');
    }
  }
  
  // Search customers
  Future<List<Customer>> searchCustomers(String search, {int limit = 10}) async {
    final queryParams = {
      'search': search,
      'limit': limit.toString(),
    };
    
    final response = await _apiService.get('/customers/search', queryParams: queryParams);
    
    return _apiService.handleListResponse(
      response,
      (json) => Customer.fromJson(json),
    );
  }
  
  // Get customer by ID
  Future<Customer> getCustomerById(int id) async {
    final response = await _apiService.get('/customers/$id');
    
    final customerResponse = _apiService.handleResponse(
      response,
      (data) => ApiResponse<Customer>.fromJson(
        data,
        (json) => Customer.fromJson(json as Map<String, dynamic>),
      ),
    );
    
    if (customerResponse.success && customerResponse.data != null) {
      return customerResponse.data!;
    } else {
      throw Exception(customerResponse.error?.message ?? 'Failed to get customer');
    }
  }
  
  // Get customer with statistics
  Future<CustomerWithStats> getCustomerWithStats(int id) async {
    final response = await _apiService.get('/customers/$id/stats');
    
    final customerResponse = _apiService.handleResponse(
      response,
      (data) => ApiResponse<CustomerWithStats>.fromJson(
        data,
        (json) => CustomerWithStats.fromJson(json as Map<String, dynamic>),
      ),
    );
    
    if (customerResponse.success && customerResponse.data != null) {
      return customerResponse.data!;
    } else {
      throw Exception(customerResponse.error?.message ?? 'Failed to get customer stats');
    }
  }
  
  // Create customer
  Future<Customer> createCustomer(CreateCustomerRequest request) async {
    final response = await _apiService.post('/customers', body: request.toJson());
    
    final customerResponse = _apiService.handleResponse(
      response,
      (data) => ApiResponse<Customer>.fromJson(
        data,
        (json) => Customer.fromJson(json as Map<String, dynamic>),
      ),
    );
    
    if (customerResponse.success && customerResponse.data != null) {
      return customerResponse.data!;
    } else {
      throw Exception(customerResponse.error?.message ?? 'Failed to create customer');
    }
  }
  
  // Update customer
  Future<Customer> updateCustomer(int id, UpdateCustomerRequest request) async {
    final response = await _apiService.put('/customers/$id', body: request.toJson());
    
    final customerResponse = _apiService.handleResponse(
      response,
      (data) => ApiResponse<Customer>.fromJson(
        data,
        (json) => Customer.fromJson(json as Map<String, dynamic>),
      ),
    );
    
    if (customerResponse.success && customerResponse.data != null) {
      return customerResponse.data!;
    } else {
      throw Exception(customerResponse.error?.message ?? 'Failed to update customer');
    }
  }
  
  // Delete customer
  Future<void> deleteCustomer(int id) async {
    final response = await _apiService.delete('/customers/$id');
    
    final deleteResponse = _apiService.handleResponse(
      response,
      (data) => ApiResponse<dynamic>.fromJson(data, (json) => json),
    );
    
    if (!deleteResponse.success) {
      throw Exception(deleteResponse.error?.message ?? 'Failed to delete customer');
    }
  }
}

class PaginatedCustomersResult {
  final List<Customer> customers;
  final Pagination pagination;
  
  PaginatedCustomersResult({
    required this.customers,
    required this.pagination,
  });
}
