import 'dart:convert';
import '../models/customer.dart';
import '../models/api_response.dart';
import 'api_service.dart';

class CustomerService {
  final ApiService _apiService = ApiService.instance;
  
  static CustomerService? _instance;
  static CustomerService get instance => _instance ??= CustomerService._();
  
  CustomerService._();
  
  // Get customers with pagination
  Future<PaginatedCustomersResult> getCustomers({
    int page = 1,
    int perPage = 10,
    String? search,
    String? name,
    String? phone,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'per_page': perPage.toString(),
    };

    if (search != null && search.isNotEmpty) {
      queryParams['search'] = search;
    }
    if (name != null && name.isNotEmpty) {
      queryParams['name'] = name;
    }
    if (phone != null && phone.isNotEmpty) {
      queryParams['phone'] = phone;
    }

    final response = await _apiService.get('/customers', queryParams: queryParams);

    // Simple manual parsing
    if (response.statusCode >= 200 && response.statusCode < 300) {
      final responseData = jsonDecode(response.body) as Map<String, dynamic>;
      final dataArray = responseData['data'] as List<dynamic>;
      final paginationData = responseData['pagination'] as Map<String, dynamic>;

      final customers = dataArray
          .map((item) => Customer.fromJson(item as Map<String, dynamic>))
          .toList();

      final pagination = Pagination.fromJson(paginationData);

      return PaginatedCustomersResult(
        customers: customers,
        pagination: pagination,
      );
    } else {
      final errorData = jsonDecode(response.body) as Map<String, dynamic>;
      final errorInfo = ErrorInfo.fromJson(errorData['error'] ?? {});
      throw ApiException(
        statusCode: response.statusCode,
        error: errorInfo,
      );
    }
  }
  
  // Search customers
  Future<List<Customer>> searchCustomers(String search, {int limit = 10}) async {
    print('🔍 Searching customers with query: $search');
    final queryParams = {
      'search': search,
      'limit': limit.toString(),
    };

    final response = await _apiService.get('/customers/search', queryParams: queryParams);
    print('🔍 Search response status: ${response.statusCode}');
    print('🔍 Search response body: ${response.body}');

    try {
      final result = _apiService.handleListResponse(
        response,
        (json) => Customer.fromJson(json),
      );
      print('✅ Search found ${result.length} customers');
      return result;
    } catch (e) {
      print('❌ Search error: $e');
      rethrow;
    }
  }
  
  // Get customer by ID
  Future<Customer> getCustomerById(int id) async {
    final response = await _apiService.get('/customers/$id');
    
    final customerResponse = _apiService.handleResponse(
      response,
      (data) => ApiResponse<Customer>.fromJson(
        data,
        (json) => Customer.fromJson(json as Map<String, dynamic>),
      ),
    );
    
    if (customerResponse.success && customerResponse.data != null) {
      return customerResponse.data!;
    } else {
      throw Exception(customerResponse.error?.message ?? 'Failed to get customer');
    }
  }
  
  // Get customer with statistics
  Future<CustomerWithStats> getCustomerWithStats(int id) async {
    print('📊 Getting customer stats for ID: $id');
    final response = await _apiService.get('/customers/$id/stats');

    print('📊 Customer stats response status: ${response.statusCode}');
    print('📊 Customer stats response body: ${response.body}');

    try {
      final customerResponse = _apiService.handleResponse(
        response,
        (data) => ApiResponse<CustomerWithStats>.fromJson(
          data,
          (json) => CustomerWithStats.fromJson(json as Map<String, dynamic>),
        ),
      );

      if (customerResponse.success && customerResponse.data != null) {
        print('✅ Customer stats loaded successfully');
        return customerResponse.data!;
      } else {
        print('❌ Customer stats failed: ${customerResponse.error?.message}');
        throw Exception(customerResponse.error?.message ?? 'Failed to get customer stats');
      }
    } catch (e) {
      print('❌ Exception in getCustomerWithStats: $e');
      rethrow;
    }
  }
  
  // Create customer
  Future<Customer> createCustomer(CreateCustomerRequest request) async {
    final response = await _apiService.post('/customers', body: request.toJson());
    
    final customerResponse = _apiService.handleResponse(
      response,
      (data) => ApiResponse<Customer>.fromJson(
        data,
        (json) => Customer.fromJson(json as Map<String, dynamic>),
      ),
    );
    
    if (customerResponse.success && customerResponse.data != null) {
      return customerResponse.data!;
    } else {
      throw Exception(customerResponse.error?.message ?? 'Failed to create customer');
    }
  }
  
  // Update customer
  Future<Customer> updateCustomer(int id, UpdateCustomerRequest request) async {
    final response = await _apiService.put('/customers/$id', body: request.toJson());
    
    final customerResponse = _apiService.handleResponse(
      response,
      (data) => ApiResponse<Customer>.fromJson(
        data,
        (json) => Customer.fromJson(json as Map<String, dynamic>),
      ),
    );
    
    if (customerResponse.success && customerResponse.data != null) {
      return customerResponse.data!;
    } else {
      throw Exception(customerResponse.error?.message ?? 'Failed to update customer');
    }
  }
  
  // Delete customer
  Future<void> deleteCustomer(int id) async {
    print('🗑️ Deleting customer ID: $id');
    final response = await _apiService.delete('/customers/$id');
    print('🗑️ Delete response status: ${response.statusCode}');
    print('🗑️ Delete response body: ${response.body}');

    if (response.statusCode >= 200 && response.statusCode < 300) {
      print('✅ Customer deleted successfully');
      return;
    } else {
      final errorData = jsonDecode(response.body) as Map<String, dynamic>;
      final errorInfo = ErrorInfo.fromJson(errorData['error'] ?? {});

      // Handle specific error cases
      if (response.statusCode == 409) {
        throw CustomerHasInvoicesException(
          'Cannot delete customer because they have existing invoices. Please delete all invoices first.',
        );
      } else {
        throw ApiException(
          statusCode: response.statusCode,
          error: errorInfo,
        );
      }
    }
  }
}

class PaginatedCustomersResult {
  final List<Customer> customers;
  final Pagination pagination;

  PaginatedCustomersResult({
    required this.customers,
    required this.pagination,
  });
}

class CustomerHasInvoicesException implements Exception {
  final String message;

  CustomerHasInvoicesException(this.message);

  @override
  String toString() => message;
}
