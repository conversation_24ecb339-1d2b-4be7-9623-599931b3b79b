import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../models/invoice.dart';
import '../providers/invoice_provider.dart';

class InvoiceFilterDialog extends StatefulWidget {
  final int customerId;

  const InvoiceFilterDialog({
    super.key,
    required this.customerId,
  });

  @override
  State<InvoiceFilterDialog> createState() => _InvoiceFilterDialogState();
}

class _InvoiceFilterDialogState extends State<InvoiceFilterDialog> {
  final _paymentController = TextEditingController();
  final _minTotalController = TextEditingController();
  final _maxTotalController = TextEditingController();
  final _searchController = TextEditingController();
  
  InvoiceStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    
    // Initialize with current filter values
    final invoiceProvider = context.read<InvoiceProvider>();
    _selectedStatus = invoiceProvider.statusFilter;
    _paymentController.text = invoiceProvider.paymentFilter ?? '';
    _minTotalController.text = invoiceProvider.minTotalFilter?.toString() ?? '';
    _maxTotalController.text = invoiceProvider.maxTotalFilter?.toString() ?? '';
    _searchController.text = invoiceProvider.searchFilter ?? '';
  }

  @override
  void dispose() {
    _paymentController.dispose();
    _minTotalController.dispose();
    _maxTotalController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _applyFilters() {
    final invoiceProvider = context.read<InvoiceProvider>();
    
    final payment = _paymentController.text.trim().isEmpty 
        ? null 
        : _paymentController.text.trim();
    
    final minTotal = _minTotalController.text.trim().isEmpty 
        ? null 
        : double.tryParse(_minTotalController.text.trim());
    
    final maxTotal = _maxTotalController.text.trim().isEmpty 
        ? null 
        : double.tryParse(_maxTotalController.text.trim());
    
    final search = _searchController.text.trim().isEmpty 
        ? null 
        : _searchController.text.trim();

    invoiceProvider.searchCustomerInvoices(
      widget.customerId,
      status: _selectedStatus,
      payment: payment,
      minTotal: minTotal,
      maxTotal: maxTotal,
      search: search,
    );

    Navigator.of(context).pop();
  }

  void _clearFilters() {
    setState(() {
      _selectedStatus = null;
      _paymentController.clear();
      _minTotalController.clear();
      _maxTotalController.clear();
      _searchController.clear();
    });
    
    context.read<InvoiceProvider>().clearFilters();
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Filter Invoices'),
      content: SizedBox(
        width: 400,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Status filter
              DropdownButtonFormField<InvoiceStatus?>(
                value: _selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'Status',
                  border: OutlineInputBorder(),
                ),
                items: [
                  const DropdownMenuItem<InvoiceStatus?>(
                    value: null,
                    child: Text('All Statuses'),
                  ),
                  ...InvoiceStatus.values.map((status) {
                    Color color;
                    IconData icon;
                    
                    switch (status) {
                      case InvoiceStatus.paid:
                        color = Colors.green;
                        icon = Icons.check_circle;
                        break;
                      case InvoiceStatus.sent:
                        color = Colors.orange;
                        icon = Icons.pending;
                        break;
                      case InvoiceStatus.canceled:
                        color = Colors.red;
                        icon = Icons.cancel;
                        break;
                    }
                    
                    return DropdownMenuItem(
                      value: status,
                      child: Row(
                        children: [
                          Icon(icon, color: color, size: 16),
                          const SizedBox(width: 8),
                          Text(status.displayName),
                        ],
                      ),
                    );
                  }),
                ],
                onChanged: (status) {
                  setState(() {
                    _selectedStatus = status;
                  });
                },
              ),
              const SizedBox(height: 16),

              // Payment method filter
              TextFormField(
                controller: _paymentController,
                decoration: const InputDecoration(
                  labelText: 'Payment Method',
                  border: OutlineInputBorder(),
                  hintText: 'e.g., Credit Card, Cash',
                ),
              ),
              const SizedBox(height: 16),

              // Amount range filters
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _minTotalController,
                      decoration: const InputDecoration(
                        labelText: 'Min Amount',
                        prefixText: '\$ ',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _maxTotalController,
                      decoration: const InputDecoration(
                        labelText: 'Max Amount',
                        prefixText: '\$ ',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Search field
              TextFormField(
                controller: _searchController,
                decoration: const InputDecoration(
                  labelText: 'Search',
                  border: OutlineInputBorder(),
                  hintText: 'Search in invoices...',
                  prefixIcon: Icon(Icons.search),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: _clearFilters,
          child: const Text('Clear All'),
        ),
        ElevatedButton(
          onPressed: _applyFilters,
          child: const Text('Apply Filters'),
        ),
      ],
    );
  }
}
