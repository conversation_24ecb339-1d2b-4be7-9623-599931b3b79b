import 'package:flutter/foundation.dart';
import '../models/api_response.dart';
import '../services/auth_service.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService.instance;
  
  UserProfile? _user;
  bool _isLoading = false;
  String? _error;
  
  UserProfile? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _user != null;
  
  // Login
  Future<bool> login(String username, String password) async {
    print('🚀 AuthProvider.login called with username: $username');
    _setLoading(true);
    _clearError();

    try {
      print('⏳ Calling auth service login...');
      final loginResponse = await _authService.login(username, password);
      print('✅ Login successful, user: ${loginResponse.user.username}');
      _user = loginResponse.user;
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      print('❌ Login failed: $e');
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }
  
  // Get profile
  Future<void> getProfile() async {
    _setLoading(true);
    _clearError();
    
    try {
      _user = await _authService.getProfile();
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }
  
  // Logout
  Future<void> logout() async {
    await _authService.logout();
    _user = null;
    _clearError();
    notifyListeners();
  }
  
  // Check if logged in
  Future<void> checkAuthStatus() async {
    final isLoggedIn = await _authService.isLoggedIn();
    if (isLoggedIn) {
      try {
        await getProfile();
      } catch (e) {
        // Token might be expired, logout
        await logout();
      }
    }
  }
  
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }
  
  void _clearError() {
    _error = null;
  }
}
