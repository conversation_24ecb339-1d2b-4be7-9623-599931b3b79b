import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/customer.dart';
import '../models/invoice.dart';
import '../providers/customer_provider.dart';
import '../providers/invoice_provider.dart';
import '../widgets/invoice_form_dialog.dart';
import '../widgets/invoice_filter_dialog.dart';

class CustomerDetailScreen extends StatefulWidget {
  final Customer customer;

  const CustomerDetailScreen({
    super.key,
    required this.customer,
  });

  @override
  State<CustomerDetailScreen> createState() => _CustomerDetailScreenState();
}

class _CustomerDetailScreenState extends State<CustomerDetailScreen> {
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    
    // Load customer stats and invoices
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final customerProvider = context.read<CustomerProvider>();
      final invoiceProvider = context.read<InvoiceProvider>();
      
      customerProvider.loadCustomerStats(widget.customer.id);
      invoiceProvider.loadCustomerInvoices(widget.customer.id);
      invoiceProvider.loadInvoiceStats(widget.customer.id);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      context.read<InvoiceProvider>().loadMoreInvoices();
    }
  }

  void _showAddInvoiceDialog() {
    print('📝 Add Invoice button pressed for customer: ${widget.customer.id}');
    showDialog(
      context: context,
      builder: (context) => InvoiceFormDialog(
        customerId: widget.customer.id,
        onSave: (request) async {
          print('💾 Saving invoice: ${request.runtimeType}');
          final navigator = Navigator.of(context);
          final messenger = ScaffoldMessenger.of(context);
          final invoiceProvider = context.read<InvoiceProvider>();

          final success = await invoiceProvider.createInvoice(request);
          print('📋 Invoice creation result: $success');
          if (success && mounted) {
            navigator.pop();
            messenger.showSnackBar(
              const SnackBar(
                content: Text('Invoice created successfully'),
                backgroundColor: Colors.green,
              ),
            );
          } else {
            print('❌ Invoice creation failed: ${invoiceProvider.error}');
          }
        },
      ),
    );
  }

  void _showEditInvoiceDialog(Invoice invoice) {
    showDialog(
      context: context,
      builder: (context) => InvoiceFormDialog(
        customerId: widget.customer.id,
        invoice: invoice,
        onSave: (request) async {
          final navigator = Navigator.of(context);
          final messenger = ScaffoldMessenger.of(context);
          final invoiceProvider = context.read<InvoiceProvider>();

          final success = await invoiceProvider.updateInvoice(invoice.id, request);
          if (success && mounted) {
            navigator.pop();
            messenger.showSnackBar(
              const SnackBar(
                content: Text('Invoice updated successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
      ),
    );
  }

  void _deleteInvoice(Invoice invoice) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Invoice'),
        content: Text('Are you sure you want to delete invoice #${invoice.id}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final messenger = ScaffoldMessenger.of(context);
              final invoiceProvider = context.read<InvoiceProvider>();

              navigator.pop();
              final success = await invoiceProvider.deleteInvoice(invoice.id);
              if (success && mounted) {
                messenger.showSnackBar(
                  const SnackBar(
                    content: Text('Invoice deleted successfully'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => InvoiceFilterDialog(
        customerId: widget.customer.id,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.customer.name),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: 'Filter Invoices',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddInvoiceDialog,
            tooltip: 'Add Invoice',
          ),
        ],
      ),
      body: Column(
        children: [
          // Customer info and stats
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[50],
            child: Column(
              children: [
                // Customer basic info
                Row(
                  children: [
                    CircleAvatar(
                      radius: 30,
                      backgroundColor: Colors.blue,
                      child: Text(
                        widget.customer.name.isNotEmpty 
                            ? widget.customer.name[0].toUpperCase() 
                            : 'C',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.customer.name,
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          if (widget.customer.phone.isNotEmpty)
                            Text('📞 ${widget.customer.phone}'),
                          if (widget.customer.address.isNotEmpty)
                            Text('📍 ${widget.customer.address}'),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Invoice statistics
                Consumer2<CustomerProvider, InvoiceProvider>(
                  builder: (context, customerProvider, invoiceProvider, child) {
                    final stats = invoiceProvider.stats;
                    
                    if (customerProvider.isLoadingStats || invoiceProvider.isLoadingStats) {
                      return const Center(child: CircularProgressIndicator());
                    }
                    
                    if (stats == null) {
                      return const SizedBox.shrink();
                    }
                    
                    return Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            'Total Invoices',
                            stats.totalCount.toString(),
                            Icons.receipt_long,
                            Colors.blue,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildStatCard(
                            'Total Amount',
                            NumberFormat.currency(symbol: '\$').format(stats.totalAmount),
                            Icons.attach_money,
                            Colors.green,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildStatCard(
                            'Paid',
                            stats.paidCount.toString(),
                            Icons.check_circle,
                            Colors.green,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildStatCard(
                            'Pending',
                            stats.sentCount.toString(),
                            Icons.pending,
                            Colors.orange,
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
          
          // Invoices list
          Expanded(
            child: Consumer<InvoiceProvider>(
              builder: (context, invoiceProvider, child) {
                if (invoiceProvider.isLoading && invoiceProvider.invoices.isEmpty) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (invoiceProvider.error != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error, size: 64, color: Colors.red),
                        const SizedBox(height: 16),
                        Text(
                          invoiceProvider.error!,
                          textAlign: TextAlign.center,
                          style: const TextStyle(color: Colors.red),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            invoiceProvider.loadCustomerInvoices(widget.customer.id);
                          },
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                if (invoiceProvider.invoices.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.receipt_outlined, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          'No invoices found',
                          style: TextStyle(fontSize: 18, color: Colors.grey),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: invoiceProvider.invoices.length + 
                      (invoiceProvider.isLoading ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == invoiceProvider.invoices.length) {
                      return const Center(
                        child: Padding(
                          padding: EdgeInsets.all(16.0),
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }

                    final invoice = invoiceProvider.invoices[index];
                    return _buildInvoiceCard(invoice);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceCard(Invoice invoice) {
    Color statusColor;
    IconData statusIcon;
    
    switch (invoice.status) {
      case InvoiceStatus.paid:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case InvoiceStatus.sent:
        statusColor = Colors.orange;
        statusIcon = Icons.pending;
        break;
      case InvoiceStatus.canceled:
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(statusIcon, color: statusColor),
        title: Row(
          children: [
            Text('Invoice #${invoice.id}'),
            const Spacer(),
            Text(
              NumberFormat.currency(symbol: '\$').format(invoice.total),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: statusColor),
                  ),
                  child: Text(
                    invoice.status.displayName,
                    style: TextStyle(
                      color: statusColor,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text('Payment: ${invoice.payment}'),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              'Created: ${DateFormat('MMM dd, yyyy').format(invoice.created)}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _showEditInvoiceDialog(invoice);
                break;
              case 'delete':
                _deleteInvoice(invoice);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 16),
                  SizedBox(width: 8),
                  Text('Edit'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 16, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
